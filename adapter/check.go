package adapter

var (
// CDTIME                 = int64(60)
// cachecdt               = &sync.Map{}
// youtube                = "https://www.youtube.com/"
)

//func (this *Proxy) URLTest(ctx context.Context, url string) (uint16, error) {
//	if nil == ctx {
//		var cancel context.CancelFunc
//		ctx, cancel = context.WithTimeout(context.Background(), 8*time.Second)
//		defer cancel()
//	}
//
//	return this.makeTest(ctx, url)
//}
