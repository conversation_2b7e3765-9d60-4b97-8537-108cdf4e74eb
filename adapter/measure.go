package adapter

import (
	"bytes"
	C "clash-foss/constant"
	"context"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"time"
)

var (
	DownloadTestBufferSize = 1500
	bufferFlag1            = []byte("font-size")
	bufferFlag2            = []byte("ytcfg")
	bufferFlag3            = []byte("yterr")
	bufferFlag4            = []byte("robot")
	bufferFlag5            = []byte("true")
)

type IMeasure interface {
	request() (*http.Request, error)
	onResponse(response *http.Response, err error) error
}

func NewProxyMeasure(ck IMeasure, c *Proxy) *ProxyMeasure {
	return &ProxyMeasure{ck, c, 0}
}

type ProxyMeasure struct {
	IMeasure
	proxy *Proxy
	tss   uint16
}
type YoutubeV1 struct {
	ProxyMeasure
}

type YoutubeV2 struct {
	ProxyMeasure
}

type Telegram struct {
	ProxyMeasure
}

type GoogleV1 struct {
	ProxyMeasure
}

func (this *GoogleV1) request() (*http.Request, error) {
	url := "https://www.gstatic.com/generate_204"
	return http.NewRequest(http.MethodHead, url, nil)
}

func (this *GoogleV1) onResponse(response *http.Response, err error) error {
	this.tss += 300
	if response.StatusCode == 204 {
		return nil
	}
	return fmt.Errorf("code-error: %d", response.StatusCode)
}

func (this *Telegram) request() (*http.Request, error) {
	url := "https://t.me/v/"
	return http.NewRequest(http.MethodGet, url, nil)
}

func (this *Telegram) onResponse(response *http.Response, err error) error {
	this.tss += 200
	if response.StatusCode != 200 {
		err = fmt.Errorf("code-error: %d", response.StatusCode)
	}
	body, _ := io.ReadAll(response.Body)
	if nil != body && bytes.Contains(body, bufferFlag5) {
		return nil
	}
	return err
}

func (this *YoutubeV1) request() (*http.Request, error) {
	url := "https://www.youtube.com/"
	return http.NewRequest(http.MethodHead, url, nil)
}
func (this *YoutubeV1) onResponse(response *http.Response, err error) error {
	//flag1 := "csp.withgoogle"
	//flag2 := "/answer/"
	flag3 := ".youtube.com"
	this.tss += 300
	for _, v := range response.Header {
		if 1 > len(v) {
			continue
		}
		v1 := strings.ToLower(v[0])
		if strings.Contains(v1, flag3) {
			return nil
		}
	}
	if nil == err {
		err = fmt.Errorf("file not found")
	}
	return err
}
func (this *YoutubeV2) request() (*http.Request, error) {
	url := "https://www.youtube.com/"
	return http.NewRequest(http.MethodGet, url, nil)
}
func (this *YoutubeV2) onResponse(response *http.Response, err error) error {
	if response.StatusCode != 200 {
		return fmt.Errorf("code-error: %d", response.StatusCode)
	}

	size := int64(0)
	buf := bytes.NewBuffer(make([]byte, 0, DownloadTestBufferSize))
	size, err = buf.ReadFrom(response.Body)

	if size > 0 {
		err = nil
		if !bytes.Contains(buf.Bytes(), bufferFlag1) {
			if !bytes.Contains(buf.Bytes(), bufferFlag2) && !bytes.Contains(buf.Bytes(), bufferFlag3) {
				err = fmt.Errorf("download 404")
			}
		}
	}
	return err
}

func (this *ProxyMeasure) check(ctx context.Context) (uint16, error) {
	//if nil == ctx {
	var cancel context.CancelFunc
	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	//}

	var addr C.Metadata
	var instance C.Conn
	var req *http.Request
	var response *http.Response

	req, err := this.request()
	if err != nil {
		return 0, err
	}

	addr, err = urlToMetadata(req.URL.String())
	if err != nil {
		return 0, err
	}

	instance, err = this.proxy.DialContext(ctx, &addr)
	if err != nil {
		return 0, err
	}
	defer instance.Close()

	req = req.WithContext(ctx)

	transport := &http.Transport{
		Dial: func(string, string) (net.Conn, error) {
			return instance, nil
		},
		// from http.DefaultTransport
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	client := http.Client{
		Transport: transport,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}
	defer client.CloseIdleConnections()

	//FX 连接成功后才开始计算时间
	start := time.Now()

	t := uint16(0)
	response, err = client.Do(req)
	if nil == response {
		return t, err
	}

	err = this.onResponse(response, err)

	if nil == err {
		t = uint16(time.Since(start)/time.Millisecond) + this.tss

		//记录时间
		this.proxy.Quality().OnTransfer(int64(DownloadTestBufferSize), err)
	}

	return t, err
}

func (this *Proxy) makeTest(ctx context.Context, url string) (t uint16, err error) {
	t = 0
	var checkers []*ProxyMeasure

	defer func() {
		this.alive.Store(err == nil)
		record := C.DelayHistory{Time: time.Now()}
		if err == nil {
			record.Delay = t
		}
		this.history.Put(record)
		if this.history.Len() > 10 {
			this.history.Pop()
		}
		this.quality.Delay = t
	}()

	if "c" == url {
		checkers = []*ProxyMeasure{
			NewProxyMeasure(&Telegram{}, this),
			NewProxyMeasure(&YoutubeV1{}, this),
			NewProxyMeasure(&GoogleV1{}, this),
		}
	} else {
		checkers = []*ProxyMeasure{
			NewProxyMeasure(&Telegram{}, this),
			NewProxyMeasure(&YoutubeV1{}, this),
			NewProxyMeasure(&YoutubeV2{}, this),
			NewProxyMeasure(&GoogleV1{}, this),
		}
	}
	rand.Shuffle(len(checkers), func(i, j int) {
		checkers[i], checkers[j] = checkers[j], checkers[i]
	})
	for i := 0; i < 3; i++ {
		c := checkers[i]
		t, err = c.check(ctx)
		if nil == err {
			return t, err
		}
	}

	return 0, err
}
