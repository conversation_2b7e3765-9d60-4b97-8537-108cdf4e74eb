package adapter

var (
	maxDelay = uint16(65535)
)

func (this *Proxy) Score() float32 {
	ss := this.quality
	rok := float32(ss.Success.Load())
	ree := float32(ss.Error.Load())
	rt := rok + ree

	delay := ss.Delay

	//请求成功率
	ratio1 := float32(1)
	ratio2 := float32(0)
	if rt > 0 {
		ratio1 = rok / rt
	}

	//延迟
	if delay > 0 {
		ratio2 = float32(1000) / float32(delay) * 0.5
	}

	//是否连续出错
	ratio3 := float32(0.5)
	if ss.ErrorCount > 0 {
		ratio3 -= float32(ss.ErrorCount / 5)
		//if 0 > ratio3 {
		//	ratio3 = 0
		//}
	}

	//连续成功率
	ratio4 := float32(0)
	//m1 := float32(ss.lastOkcc + ss.lastEcc)
	//if m1 > 0 {
	//	ratio4 = float32(ss.lastOkcc) / float32(ss.lastOkcc+ss.lastEcc)
	//}

	//平均每次字节数
	ratio5 := float32(0)
	//if rok > 0 {
	//	t := float32(ss.transfer.Load())
	//	ratio5 = t / rt / float32(1024)
	//}

	//速度加成
	ratio6 := float32(1)
	if ss.MaxSpeed > 0 {
		if ss.ErrorCount > 0 {
			ratio6 -= float32(ss.ErrorCount) * 0.1
		}
		ratio6 += (float32(ss.MaxSpeed) / 100000) * 0.7
	}
	//else {
	//	//用时占比
	//	a := ss.Delay
	//	if 1 > a {
	//		a = maxDelay
	//	}
	//	ratio2 = float32(maxDelay-a) / float32(maxDelay) * 0.5
	//}

	//if len(show) > 0 {
	//	log.Errorln("\n=======RATIO==>", ratio1, ratio2, ratio3, ratio4, ratio5, ratio6)
	//}

	return ratio1 + ratio2 + ratio3 + ratio4 + ratio5 + ratio6
}
