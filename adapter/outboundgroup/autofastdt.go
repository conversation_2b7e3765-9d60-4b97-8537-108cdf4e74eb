package outboundgroup

import (
	"clash-foss/log"
	"clash-foss/tunnel"
	"context"
	"encoding/json"
	"time"

	"clash-foss/adapter/outbound"
	"clash-foss/common/singledo"
	"clash-foss/component/dialer"
	C "clash-foss/constant"
	"clash-foss/constant/provider"
)

var (
	fastSingle                 = singledo.NewSingle(time.Second * 10)
	singleProxies              = singledo.NewSingle(time.Second * 3)
	_lbs          *LoadBalance = nil
	currentNode   C.Proxy      = nil
)

type AutoFast struct {
	*outbound.Base
	disableUDP bool
	single     *singledo.Single
}

func (lb *AutoFast) Now() string {
	if nil == currentNode {
		return ""
	}
	return currentNode.Name()
}

func autoFastProxies(size int) []C.Proxy {
	min := 3
	proxies, _, _ := singleProxies.Do(func() (interface{}, error) {
		elms := tunnel.LoadSortedProxies(size)
		total := len(elms)
		//保证最少有N代理
		if min >= total {
			return elms, nil
		}

		//最后最后一名的速度
		last := elms[total-1]
		if last.Score() > 0 {
			return elms, nil
		}

		//除去分数太低代理
		j := 0
		for i, elm := range elms {
			if 0 > elm.Score() {
				j = i
				break
			}
		}
		if min > j {
			j = min
		}

		log.Errorln("=======NEW FAST===> %d", j)

		return elms[0:j], nil
	})

	elms := proxies.([]C.Proxy)
	if nil == elms || min > len(elms) {
		singleProxies.Reset()
	}

	return elms
}

// DialContext implements C.ProxyAdapter
func (lb *AutoFast) DialContext(ctx context.Context, metadata *C.Metadata, opts ...dialer.Option) (c C.Conn, err error) {
	defer func() {
		if err == nil && nil != c {
			c.AppendToChains(lb)
		}
	}()

	proxy := lb.Unwrap(metadata)

	c, err = proxy.DialContext(ctx, metadata, lb.Base.DialOptions(opts...)...)

	//使用其它代理再试一次
	if err != nil {
		proxy.Quality().Report(err)
		if elm := FetchFast(true); nil != elm {
			proxy = elm.(C.Proxy)
			c, err = proxy.DialContext(ctx, metadata)
			if nil != err {
				proxy.Quality().Report(err)
			}
			log.Debugln("[Fast]Dial proxy change to %s [%d]", proxy.Name(), proxy.LastDelay())
		}
	}
	return
}

// ListenPacketContext implements C.ProxyAdapter
func (lb *AutoFast) ListenPacketContext(ctx context.Context, metadata *C.Metadata, opts ...dialer.Option) (pc C.PacketConn, err error) {
	defer func() {
		if err == nil {
			pc.AppendToChains(lb)
		}
	}()

	proxy := lb.Unwrap(metadata)
	pc, err = proxy.ListenPacketContext(ctx, metadata, lb.Base.DialOptions(opts...)...)

	//使用其它代理再试一次
	if err != nil {
		proxy.Quality().Report(err)
		if elm := FetchFast(true); nil != elm {
			proxy = elm.(C.Proxy)
			pc, err = proxy.ListenPacketContext(ctx, metadata, lb.Base.DialOptions(opts...)...)
			if nil != err {
				proxy.Quality().Report(err)
			}
		}
	}
	return
}

// SupportUDP implements C.ProxyAdapter
func (lb *AutoFast) SupportUDP() bool {
	if lb.disableUDP {
		return false
	}

	if nil != fastNode {
		return fastNode.SupportUDP()
	}
	if fastNodes.isEmpty() {
		return fastNodes.fast().SupportUDP()
	}

	return !lb.disableUDP
}

// Unwrap implements C.ProxyAdapter
func (lb *AutoFast) Unwrap(metadata *C.Metadata) C.Proxy {
	//10.24测试使用新方式
	if nil != innerLoadbs {
		return innerLoadbs.Unwrap(metadata)
	}

	return FetchFast(false)
}

// MarshalJSON implements C.ProxyAdapter
func (lb *AutoFast) MarshalJSON() ([]byte, error) {
	var all []string
	for _, proxy := range fastNodes.nodes {
		all = append(all, proxy.Name())
	}
	return json.Marshal(map[string]interface{}{
		"type": lb.Type().String(),
		"all":  all,
	})
}

func FetchFast(force bool) C.Proxy {
	elm, _, _ := fastSingle.Do(func() (interface{}, error) {
		//if !force && fastNode != nil {
		//	if fastNode.LastDelay() < fastNodeDelay+AutoFastDelayMax {
		//		return fastNode, nil
		//	}
		//}
		//
		//fast := fastNodes.next()
		//
		//if nil == fast {
		//	proxies := tunnel.LoadSortedProxies(1)
		//	if len(proxies) > 0 {
		//		fast = proxies[0]
		//	}
		//}
		//
		//if nil == fast {
		//	proxies := tunnel.LoadOnlyProxies()
		//	fast = proxies[0]
		//	min := fast.LastDelay()
		//	for _, proxy := range proxies[1:] {
		//		if !proxy.Alive() {
		//			continue
		//		}
		//
		//		delay := proxy.LastDelay()
		//		if delay < min {
		//			fast = proxy
		//			min = delay
		//		}
		//	}
		//}
		var fast C.Proxy
		proxies := tunnel.LoadSortedProxies(1)
		if len(proxies) > 0 {
			fast = proxies[0]
		}

		fastNode = fast
		return fast, nil
	})

	proxy := elm.(C.Proxy)
	if !proxy.Alive() {
		fastSingle.Reset()
	}
	currentNode = proxy
	return proxy
}

func NewAutoFast(option *GroupCommonOption, providers []provider.ProxyProvider) *AutoFast {
	//尝试在快速列表里做负载均衡
	strategy := "round-robin"
	//strategy := "consistent-hashing"
	_lbs, _ = NewLoadBalance(option, providers, strategy)

	C.AutoFastReloader = func() {
		fastSingle.Reset()
	}

	//TEST 默认使用多个线路
	AutoFastStrategyMode = 2
	AutoFastReload()

	return &AutoFast{
		Base: outbound.NewBase(outbound.BaseOption{
			Name:        option.Name,
			Type:        C.AutoFast,
			Interface:   option.Interface,
			RoutingMark: option.RoutingMark,
		}),
		single:     singledo.NewSingle(time.Second * 30),
		disableUDP: option.DisableUDP,
	}
}
