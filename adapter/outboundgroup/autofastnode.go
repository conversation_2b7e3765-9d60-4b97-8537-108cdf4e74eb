package outboundgroup

import (
	"clash-foss/common/murmur3"
	C "clash-foss/constant"
	"clash-foss/log"
	"clash-foss/tunnel"
	"context"
	"fmt"
	"sync"
	"time"
)

var (
	fastNode             C.Proxy = nil
	fastNodes                    = fastNodeList{nodes: make([]C.Proxy, 0)}
	fastNodeDelay                = uint16(0)
	serviceStart                 = false
	cacheMux             sync.RWMutex
	AutoFastProxySize                 = 20
	AutoFastInterval                  = 300
	CheckCoolDownTime                 = 10
	AutoFastDelayMax                  = uint16(70)
	fastCheckResult                   = make(chan fastResult)
	innerLoadbs          *LoadBalance = nil
	AutoFastStrategyMode              = 0
)

func AutoFastReload() {
	fastNode = nil
	innerLoadbs = nil
	if nil == _lbs {
		return
	}
	var strategy strategyFn

	//尝试在快速列表里做负载均衡
	switch AutoFastStrategyMode {
	case 0:
		strategy = nil
	case 1:
		strategy = fxstrategyConsistentHashing()
	default:
		strategy = fxstrategyRoundRobin()
	}
	if nil != strategy {
		_lbs.strategyFn = strategy
		innerLoadbs = _lbs
	}
}

type fastNodeList struct {
	index int
	nodes []C.Proxy
	size  int
}
type Node C.Proxy
type fastResult struct {
	act  int
	node C.Proxy
	time uint16
}

func startNodeService() {
	//go proxyLoaderService()
	fastLoaderService()
}

func (this *fastNodeList) add(elm C.Proxy) {
	this.nodes = append(this.nodes, elm)
	this.size = len(this.nodes)
}

func (this *fastNodeList) set(elm C.Proxy) {
	this.nodes = []C.Proxy{elm}
	this.size = len(this.nodes)
}
func (this *fastNodeList) isEmpty() bool {
	return 1 > this.size
}
func (this *fastNodeList) fast() C.Proxy {
	if 1 > this.size {
		return nil
	}
	return this.nodes[0]
}

func (this *fastNodeList) next() C.Proxy {
	if 1 > this.size {
		return nil
	}
	for i := 0; i < this.size; i++ {
		c := this.index % this.size
		this.index += 1
		elm := this.nodes[c]
		if elm.Alive() {
			return elm
		}
	}

	return nil
}

func getFastNodes() []C.Proxy {
	size := len(fastNodes.nodes)
	var items = make([]C.Proxy, 0, size+2)
	if nil != fastNode {
		items = append(items, fastNode)
	}
	for _, proxy := range fastNodes.nodes {
		items = append(items, proxy)
	}
	return items
}

func fastResultWaiterV2() {
	index := 0
	for {
		r := <-fastCheckResult
		if r.act == 9 {
			index = 0
			continue
		}

		if nil == r.node || !r.node.Alive() {
			continue
		}

		index += 1

		if 1 > fastNodeDelay || fastNodeDelay > r.node.LastDelay() {
			fastNode = r.node
			fastNodeDelay = r.time
		}

		if 1 == index {
			fastNodes.set(r.node)
		} else {
			fastNodes.add(r.node)
		}
	}
}

func sortFastNodes() {
	fastNodes.nodes = tunnel.SortProxies(fastNodes.nodes)
}

func fastResultWaiter() {
	index := 0
	for {
		r := <-fastCheckResult
		if r.act == 9 {
			index = 0
			continue
		}

		fmt.Println("==========DT-RANK===>", index, r.time, r.node.Alive(), r.node.Name())

		if nil == r.node || !r.node.Alive() {
			continue
		}

		//先检查一下本轮最快跟现有最快
		if 1 > index && fastNodeDelay > 0 {
			fst := r.node
			//如果同一代理
			if fst.Name() == fastNode.Name() {
				index += 1
				continue
			}
			//不替换：如果新代理还不如旧的快
			if fst.LastDelay() > fastNodeDelay {
				index = 1
			}
		}

		if 1 > index {
			//本轮最快的节点
			fastNode = r.node
			fastNodeDelay = r.time
		} else if 1 == index {
			//第二快的节点
			fastNodes.set(r.node)
		} else {
			//其它节点
			fastNodes.add(r.node)
		}

		index += 1
	}
}

func fastDownloadTest(ctx context.Context, elm C.Proxy) {
	ts, _, err := elm.URLTest(ctx, "c")
	if err == nil {
		fastCheckResult <- fastResult{act: 1, node: elm, time: ts}
	} else {
		//fmt.Println("===fastDownloadTest======>", elm.Name(), err.Error())
	}
}

func FastProxyLoader() {
	fastSingle.Reset()
	if !cacheMux.TryLock() {
		return
	}

	items := tunnel.LoadSortedProxies(AutoFastProxySize)

	log.Errorln("=======FastProxyLoader====start==>")

	//特殊重置指令
	fastCheckResult <- fastResult{act: 9, node: nil}

	bSize := 10
	total := len(items)
	for i := 0; i < total; i += bSize {
		c := i + bSize
		if c > total {
			c = total
		}
		batchCheck(items[i:c])
	}

	//ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	//defer cancel()
	//for j := range items {
	//	elm := items[j]
	//	go fastDownloadTest(ctx, elm)
	//}
	//<-ctx.Done()

	sortFastNodes()

	//N秒内不允许测试第二次
	if CheckCoolDownTime > 0 {
		time.Sleep(time.Second * time.Duration(CheckCoolDownTime))
	}

	cacheMux.Unlock()
}

func batchCheck(items []C.Proxy) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	for j := range items {
		elm := items[j]
		go fastDownloadTest(ctx, elm)
	}

	<-ctx.Done()
}

func fastLoaderService() {
	if serviceStart {
		return
	}
	serviceStart = true
	go fastResultWaiterV2()

	for {
		//等待有足够代码时再执行选举
		time.Sleep(1 * time.Second)
		if tunnel.LoadProxiesSize() > 5 {
			break
		}
	}

	FastProxyLoader()

	ticker := time.NewTicker(time.Duration(AutoFastInterval) * time.Second)
	for {
		select {
		case <-ticker.C:
			FastProxyLoader()
		}
	}
}

func fxstrategyRoundRobin() strategyFn {
	idx := 0
	return func(proxies []C.Proxy, metadata *C.Metadata) (elm C.Proxy) {
		length := len(proxies)
		for i := 0; i < length; i++ {
			idx = (idx + 1) % length
			return proxies[idx]
		}

		return proxies[0]
	}
}

func fxstrategyConsistentHashing() strategyFn {
	maxRetry := 5
	return func(proxies []C.Proxy, metadata *C.Metadata) (elm C.Proxy) {
		key := uint64(murmur3.Sum32([]byte(getKey(metadata))))
		buckets := int32(len(proxies))
		for i := 0; i < maxRetry; i, key = i+1, key+1 {
			idx := jumpHash(key, buckets)
			return proxies[idx]
		}

		// when availability is poor, traverse the entire list to get the available nodes
		for _, proxy := range proxies {
			if proxy.Alive() {
				return proxy
			}
		}

		return proxies[0]
	}
}
