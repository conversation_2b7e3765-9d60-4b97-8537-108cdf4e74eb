package outboundgroup

import (
	"clash-foss/log"
	"context"
	"encoding/json"
	"time"

	"clash-foss/adapter/outbound"
	"clash-foss/common/singledo"
	"clash-foss/component/dialer"
	C "clash-foss/constant"
	"clash-foss/constant/provider"
)

var (
	nextChange = false
)

type Fast struct {
	*outbound.Base
	disableUDP bool
	single     *singledo.Single
	providers  []provider.ProxyProvider

	fastSingle *singledo.Single
	tolerance  uint16
	lastDelay  uint16
	fastNode   C.Proxy

	AfId int
}

func FastReload() {
	nextChange = true

}

func (f *Fast) Now() string {
	proxy := f.findAliveProxy(false)
	return proxy.Name()
}

// DialContext implements C.ProxyAdapter
func (f *Fast) DialContext(ctx context.Context, metadata *C.Metadata, opts ...dialer.Option) (C.Conn, error) {
	proxy := f.findAliveProxy(true)
	c, err := proxy.DialContext(ctx, metadata, f.Base.DialOptions(opts...)...)

	//使用其它代理再试一次
	if err != nil {
		if tmp := f.fast(true); nil != tmp {
			proxy = tmp.(C.Proxy)
			c, err = proxy.DialContext(ctx, metadata)
			log.Debugln("[Fast]Dial proxy change to %s [%d]", f.Name(), proxy.LastDelay())
		}
	}

	if err == nil {
		c.AppendToChains(f)
	}
	return c, err
}

// ListenPacketContext implements C.ProxyAdapter
func (f *Fast) ListenPacketContext(ctx context.Context, metadata *C.Metadata, opts ...dialer.Option) (C.PacketConn, error) {
	proxy := f.findAliveProxy(true)
	pc, err := proxy.ListenPacketContext(ctx, metadata, f.Base.DialOptions(opts...)...)

	//使用其它代理再试一次
	if err != nil {
		if tmp := f.fast(true); nil != tmp {
			proxy = tmp.(C.Proxy)
			pc, err = proxy.ListenPacketContext(ctx, metadata, f.Base.DialOptions(opts...)...)
			log.Debugln("[Fast]UDP proxy change to %s", f.Name())
		}
	}

	if err == nil {
		pc.AppendToChains(f)
	}
	return pc, err
}

// SupportUDP implements C.ProxyAdapter
func (f *Fast) SupportUDP() bool {
	if f.disableUDP {
		return false
	}

	proxy := f.findAliveProxy(false)
	return proxy.SupportUDP()
}

// MarshalJSON implements C.ProxyAdapter
func (f *Fast) MarshalJSON() ([]byte, error) {
	var all []string
	for _, proxy := range f.proxies(false) {
		all = append(all, proxy.Name())
	}
	return json.Marshal(map[string]interface{}{
		"type": f.Type().String(),
		"now":  f.Now(),
		"all":  all,
	})
}

// Unwrap implements C.ProxyAdapter
func (f *Fast) Unwrap(metadata *C.Metadata) C.Proxy {
	proxy := f.findAliveProxy(true)
	return proxy
}

func (f *Fast) proxies(touch bool) []C.Proxy {
	elm, _, _ := f.single.Do(func() (interface{}, error) {
		return getProvidersProxies(f.providers, touch), nil
	})

	return elm.([]C.Proxy)
}

func (f *Fast) findAliveProxy(touch bool) C.Proxy {
	p := f.fast(false)
	if nil == p {
		p = f.fast(true)
	}
	if nil == p {
		return f.getAliveProxy(touch)
	}
	return p.(C.Proxy)
}
func (f *Fast) getAliveProxy(touch bool) C.Proxy {
	proxies := f.proxies(touch)
	for _, proxy := range proxies {
		if proxy.Alive() {
			return proxy
		}
	}

	return proxies[0]
}

func NewFast(option *GroupCommonOption, providers []provider.ProxyProvider) *Fast {
	return &Fast{
		Base: outbound.NewBase(outbound.BaseOption{
			Name:        option.Name,
			Type:        C.AutoFast,
			Interface:   option.Interface,
			RoutingMark: option.RoutingMark,
		}),
		single:     singledo.NewSingle(defaultGetProxiesDuration),
		fastSingle: singledo.NewSingle(time.Second * 10),
		tolerance:  150,
		providers:  providers,
		disableUDP: option.DisableUDP,
	}
}

func (u *Fast) fast(force bool) C.Proxy {
	elm, _, _ := u.fastSingle.Do(func() (interface{}, error) {
		// tolerance
		if !force && u.tolerance != 0 && u.fastNode != nil && !nextChange {
			if u.fastNode.LastDelay() < u.lastDelay+u.tolerance {
				return u.fastNode, nil
			}
		}

		proxies := u.proxies(true)
		fast := proxies[0]
		min := fast.LastDelay()
		for _, proxy := range proxies[1:] {
			if !proxy.Alive() {
				continue
			}

			delay := proxy.LastDelay()
			if delay < min {
				fast = proxy
				min = delay
			}
		}

		nextChange = false
		u.fastNode = fast
		u.lastDelay = fast.LastDelay()
		return fast, nil
	})
	return elm.(C.Proxy)
}
