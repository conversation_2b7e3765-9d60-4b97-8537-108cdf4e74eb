package outboundgroup

/*
import (
	"context"
	"encoding/json"
	"clash-foss/log"
	"clash-foss/tunnel"
	"net"
	"sync"
	"time"

	"clash-foss/adapter/outbound"
	"clash-foss/common/murmur3"
	"clash-foss/common/singledo"
	"clash-foss/component/dialer"
	C "clash-foss/constant"
	"clash-foss/constant/provider"

	"golang.org/x/net/publicsuffix"
)

var (
	AutoFastProxySize    = 10
	AutoFastInterval     = 30
	AutoFastStrategyMode = 0
	AutoFastDelayMax     = uint16(70)

	cacheMux      sync.RWMutex
	cacheProxies  []C.Proxy = nil
	fastNode      C.Proxy   = nil
	fastNodeDelay           = uint16(0)
	fastSingle              = singledo.NewSingle(time.Second * 10)
)

type autoStrategyFn = func(proxies []C.Proxy, metadata *C.Metadata) C.Proxy

type AutoFast struct {
	*outbound.Base
	disableUDP  bool
	single      *singledo.Single
	strategyFn1 autoStrategyFn
	strategyFn2 autoStrategyFn
}

func AutoFastReload() {
	fastNode = nil
}

func proxyLoader() {
	cacheMux.Lock()
	items := tunnel.LoadSortedProxies(AutoFastProxySize)
	elms := make([]C.Proxy, len(items))
	index := 0
	for _, m := range items {
		if m.Alive() {
			elms[index] = m
			index += 1
		}
	}
	if index > 0 {
		cacheProxies = elms[0:index]
	}
	cacheMux.Unlock()
}

func proxyLoaderService() {
	if nil != cacheProxies {
		return
	}
	cacheProxies = make([]C.Proxy, 0)

	ticker := time.NewTicker(time.Duration(AutoFastInterval) * time.Second)

	proxyLoader()

	for {
		select {
		case <-ticker.C:
			proxyLoader()
		}
	}
}

func afXgetKey(metadata *C.Metadata) string {
	if metadata.Host != "" {
		// ip host
		if ip := net.ParseIP(metadata.Host); ip != nil {
			return metadata.Host
		}

		if etld, err := publicsuffix.EffectiveTLDPlusOne(metadata.Host); err == nil {
			return etld
		}
	}

	if metadata.DstIP == nil {
		return ""
	}

	return metadata.DstIP.String()
}

func afXjumpHash(key uint64, buckets int32) int32 {
	var b, j int64

	for j < int64(buckets) {
		b = j
		key = key*2862933555777941757 + 1
		j = int64(float64(b+1) * (float64(int64(1)<<31) / float64((key>>33)+1)))
	}

	return int32(b)
}

// DialContext implements C.ProxyAdapter
func (lb *AutoFast) DialContext(ctx context.Context, metadata *C.Metadata, opts ...dialer.Option) (c C.Conn, err error) {
	defer func() {
		if err == nil {
			c.AppendToChains(lb)
		}
	}()

	proxy := lb.Unwrap(metadata)

	c, err = proxy.DialContext(ctx, metadata, lb.Base.DialOptions(opts...)...)

	//使用其它代理再试一次
	if err != nil {
		if elm := FetchFast(true); nil != elm {
			proxy = elm.(C.Proxy)
			c, err = proxy.DialContext(ctx, metadata)
			log.Debugln("[Fast]Dial proxy change to %s [%d]", proxy.Name(), proxy.LastDelay())
		}
	}
	return
}

// ListenPacketContext implements C.ProxyAdapter
func (lb *AutoFast) ListenPacketContext(ctx context.Context, metadata *C.Metadata, opts ...dialer.Option) (pc C.PacketConn, err error) {
	defer func() {
		if err == nil {
			pc.AppendToChains(lb)
		}
	}()

	proxy := lb.Unwrap(metadata)
	pc, err = proxy.ListenPacketContext(ctx, metadata, lb.Base.DialOptions(opts...)...)

	//使用其它代理再试一次
	if err != nil {
		if elm := FetchFast(true); nil != elm {
			proxy = elm.(C.Proxy)
			pc, err = proxy.ListenPacketContext(ctx, metadata, lb.Base.DialOptions(opts...)...)
		}
	}
	return
}

// SupportUDP implements C.ProxyAdapter
func (lb *AutoFast) SupportUDP() bool {
	if lb.disableUDP {
		return false
	}

	if nil != fastNode {
		return fastNode.SupportUDP()
	}
	if len(cacheProxies) > 0 {
		return cacheProxies[0].SupportUDP()
	}

	return !lb.disableUDP
}

func afXstrategyRoundRobin() strategyFn {
	idx := 0
	return func(proxies []C.Proxy, metadata *C.Metadata) C.Proxy {
		length := len(proxies)
		for i := 0; i < length; i++ {
			idx = (idx + 1) % length
			proxy := proxies[idx]
			log.Errorln("======RoundRobin======> %s, %d", proxy.Name(), proxy.LastDelay())
			if proxy.Alive() {
				return proxy
			}
		}

		return proxies[0]
	}
}

func afXstrategyConsistentHashing() autoStrategyFn {
	maxRetry := 5
	return func(proxies []C.Proxy, metadata *C.Metadata) C.Proxy {
		key := uint64(murmur3.Sum32([]byte(afXgetKey(metadata))))
		buckets := int32(len(proxies))
		for i := 0; i < maxRetry; i, key = i+1, key+1 {
			idx := afXjumpHash(key, buckets)
			proxy := proxies[idx]
			log.Errorln("======ConsistentHashing======> %s, %d", proxy.Name(), proxy.LastDelay())
			if proxy.Alive() {

				return proxy
			}
		}

		return proxies[0]
	}
}

// Unwrap implements C.ProxyAdapter
func (lb *AutoFast) Unwrap(metadata *C.Metadata) C.Proxy {
	//defer fmt.Print("\t=====Unwrap======>", metadata.Host,"\n")

	if 0 == AutoFastStrategyMode {
		return FetchFast(false)
	}

	proxies := lb.proxies(true)
	if nil == proxies || 2 > len(proxies) {
		return FetchFast(false)
	}

	switch AutoFastStrategyMode {
	case 1:
		return lb.strategyFn1(proxies, metadata)
	case 2:
		return lb.strategyFn2(proxies, metadata)

	default:
		return FetchFast(false)
	}
}

func (lb *AutoFast) proxies(touch bool) []C.Proxy {
	elm, _, _ := lb.single.Do(func() (interface{}, error) {
		return cacheProxies, nil
	})

	return elm.([]C.Proxy)
}

// MarshalJSON implements C.ProxyAdapter
func (lb *AutoFast) MarshalJSON() ([]byte, error) {
	var all []string
	for _, proxy := range cacheProxies {
		all = append(all, proxy.Name())
	}
	return json.Marshal(map[string]interface{}{
		"type": lb.Type().String(),
		"all":  all,
	})
}

func FetchFast(force bool) C.Proxy {
	elm, _, _ := fastSingle.Do(func() (interface{}, error) {
		if !force && fastNode != nil {
			if fastNode.LastDelay() < fastNodeDelay+AutoFastDelayMax {
				return fastNode, nil
			}
		}

		proxies := tunnel.LoadOnlyProxies()
		fast := proxies[0]
		min := fast.LastDelay()
		for _, proxy := range proxies[1:] {
			if !proxy.Alive() {
				continue
			}

			delay := proxy.LastDelay()
			if delay < min {
				fast = proxy
				min = delay
			}
		}

		log.Errorln("=======fast==**new**===> %s, %d", fast.Name(), fast.LastDelay())

		//log.Debugln("[Fast]proxy change to %s [%d]", fast.Name(), fast.LastDelay())

		fastNode = fast
		fastNodeDelay = fast.LastDelay()
		return fast, nil
	})
	//return elm.(C.Proxy)

	pp := elm.(C.Proxy)
	log.Errorln("=======fast===> %s, %d", pp.Name(), pp.LastDelay())
	return pp
}

func NewAutoFast(option *GroupCommonOption, providers []provider.ProxyProvider) *AutoFast {
	go proxyLoaderService()

	return &AutoFast{
		Base: outbound.NewBase(outbound.BaseOption{
			Name:        option.Name,
			Type:        C.AutoFast,
			Interface:   option.Interface,
			RoutingMark: option.RoutingMark,
		}),
		single:      singledo.NewSingle(time.Second * 30),
		strategyFn1: afXstrategyConsistentHashing(),
		strategyFn2: afXstrategyRoundRobin(),
		disableUDP:  option.DisableUDP,
	}
}

// */
