name: （中文）提交 Clash 核心的问题
description: 如果 Clash 核心运作不符合预期，在这里提交问题
labels:
  - bug
title: "[Bug] <问题标题>"
body:
  - type: markdown
    attributes:
      value: "## 欢迎来到 Clash 官方开源社区！"

  - type: markdown
    attributes:
      value: |
        感谢你拨冗提交 Clash 内核的问题。在提交之前，请仔细阅读并遵守以下指引，以确保你的问题能够被尽快解决。  
        带有星号（*）的选项为必填，其他可选填。**如果你填写的资料不符合规范，维护者可能不予回复，并直接关闭这个 issue。**
        如果你可以自行 debug 并且修正，我们随时欢迎你提交 Pull Request，将你的修改合并到上游。

  - type: checkboxes
    id: ensure
    attributes:
      label: 先决条件
      description: "若以下任意选项不适用，请勿提交这个 issue，因为我们会把它关闭"
      options:
        - label: "我了解这里是官方开源版 Clash 核心仓库，**只提供开源版或者 Premium 内核的支持**"
          required: true
        - label: "我要提交 Clash 核心的问题，并非 Clash.Meta / OpenClash / ClashX / Clash For Windows 或其他任何衍生版本的问题"
          required: true
        - label: "我使用的是**本仓库**最新版本的 Clash 或 Clash Premium 内核"
          required: true
        - label: "我已经在 [Issue Tracker](……/) 中找过我要提出的 bug，**并且没有找到相关问题**"
          required: true
        - label: "我已经仔细阅读 [官方 Wiki](https://dreamacro.github.io/clash/) 并无法自行解决问题"
          required: true
        - label: "（非 Premium 内核必填）我已经使用 dev 分支版本测试过，问题依旧存在"
          required: false

  - type: markdown
    attributes:
      value: "## 系统环境"
  - type: markdown
    attributes:
      value: |
        请附上这个问题适用的环境，以帮助我们迅速定位问题并解决。若你提供的信息不足，我们将关闭
        这个 issue 并要求你提供更多信息。

  - type: input
    attributes:
      label: 版本
      description: "运行 `clash -v` 或者查看 Clash Dashboard 的左下角来找到你现在使用的版本"
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: 适用的作业系统
      description: "勾选所有适用于这个 issue 的系统"
      multiple: true
      options:
        - Linux
        - Windows
        - macOS (darwin)
        - Android
        - OpenBSD / FreeBSD

  - type: dropdown
    id: arch
    attributes:
      label: 适用的硬件架构
      description: "勾选所有适用于这个 issue 的架构"
      multiple: true
      options:
        - amd64
        - amd64-v3
        - arm64
        - "386"
        - armv5
        - armv6
        - armv7
        - mips-softfloat
        - mips-hardfloat
        - mipsle-softfloat
        - mipsle-hardfloat
        - mips64
        - mips64le
        - riscv64

  - type: markdown
    attributes:
      value: "## Clash 相关信息"
  - type: markdown
    attributes:
      value: |
        请附上与这个问题直接相关的相应信息，以帮助我们迅速定位问题并解决。  
        若你提供的信息不足，我们将关闭这个 issue 并要求你提供更多信息。

  - type: textarea
    attributes:
      render: YAML
      label: "配置文件"
      placeholder: "确保配置文件中没有敏感信息（如：服务器地址、密码、端口），并且提供最小可复现配置，严禁贴上上千行的配置"
    validations:
      required: true

  - type: textarea
    attributes:
      render: Text
      label: 日志输出
      placeholder: "在这里附上问题对应的内核日志（在配置中设置 `log-level: debug` 可获得调试信息）"

  - type: textarea
    attributes:
      label: 问题描述
      placeholder: "在这里详细叙述你的问题，帮助我们理解（支持 Markdown 语法）"
    validations:
      required: true

  - type: textarea
    attributes:
      label: 复现步骤
      placeholder: "在这里提供问题的具体重现步骤（支持 Markdown 语法）"
