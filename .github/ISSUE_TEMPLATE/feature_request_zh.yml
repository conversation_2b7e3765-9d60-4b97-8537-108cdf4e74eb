name: （中文）建议一个新功能
description: 在这里提供一个的想法或建议
labels:
  - enhancement
title: "[Feature] <标题>"
body:
  - type: markdown
    attributes:
      value: "## 欢迎来到 Clash 官方开源社区！"

  - type: markdown
    attributes:
      value: |
        感谢你拨冗为 Clash 内核提供建议。在提交之前，请仔细阅读并遵守以下指引，以确保你的建议能够被顺利采纳。  
        带有星号（*）的选项为必填，其他可选填。**如果你填写的资料不符合规范，维护者可能不予回复，并直接关闭这个 issue。**
        如果你可以自行添加这个功能，我们随时欢迎你提交 Pull Request，并将你的修改合并到上游。

  - type: checkboxes
    id: ensure
    attributes:
      label: 先决条件
      description: "若以下任意选项不适用，请勿提交这个 issue，因为我们会把它关闭"
      options:
        - label: "我了解这里是 Clash 官方仓库，并非 Clash.Meta / OpenClash / ClashX / Clash For Windows 或其他任何衍生版本"
          required: true
        - label: "我已经在[这里](https://github.com/Dreamacro/clash/issues?q=is%3Aissue+label%3Aenhancement)找过我要提出的建议，**并且没有找到相关问题**"
          required: true
        - label: "我已经仔细阅读 [官方 Wiki](https://dreamacro.github.io/clash/) "
          required: true

  - type: textarea
    attributes:
      label: 描述
      placeholder: 请详细、清晰地表达你要提出的论述，例如这个问题如何影响到你？你想实现什么功能？目前 Clash Core 的行为是什么？
    validations:
      required: true

  - type: textarea
    attributes:
      label: 可能的解决方案
      placeholder: 此项非必须，但是如果你有想法的话欢迎提出。
