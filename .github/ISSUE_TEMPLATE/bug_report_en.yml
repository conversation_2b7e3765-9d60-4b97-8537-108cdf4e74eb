name: (English) Report a bug of the Clash core
description: Create a bug report to help us improve
labels:
  - bug
title: "[Bug] <issue title>"
body:
  - type: markdown
    attributes:
      value: "## Welcome to the official Clash open-source community"

  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to report an issue with the Clash core.  
        
        Prior to submitting this issue, please read and follow the guidelines below to ensure that your issue can be resolved as quickly as possible. Options marked with an asterisk (*) are required, while others are optional. If the information you provide does not comply with the requirements, the maintainers may not respond and may directly close the issue.  
        
        If you can debug and fix the issue yourself, we welcome you to submit a pull request to merge your changes upstream.

  - type: checkboxes
    id: ensure
    attributes:
      label: Prerequisites
      description: "If any of the following options do not apply, please do not submit this issue as we will close it"
      options:
        - label: "I understand that this is the official open-source version of the Clash core, **only providing support for the open-source version or Premium version**"
          required: true
        - label: "I am submitting an issue with the Clash core, not Clash.Meta / OpenClash / ClashX / Clash For Windows or any other derivative version"
          required: true
        - label: "I am using the latest version of the Clash or Clash Premium core **in this repository**"
          required: true
        - label: "I have searched at the [Issue Tracker](……/) **and have not found any related issues**"
          required: true
        - label: "I have read the [official Wiki](https://dreamacro.github.io/clash/) **and was unable to solve the issue**"
          required: true
        - label: "(required for Premium core) I've tried the `dev` branch and the issue still exists"
          required: false

  - type: markdown
    attributes:
      value: "## Environment"
  - type: markdown
    attributes:
      value: |
        Please provide the following information to help us locate the issue.  
        The issue might be closed if there's not enough information provided.

  - type: input
    attributes:
      label: Version
      description: "Run `clash -v` or look at the bottom-left corner of the Clash Dashboard to find out"
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: "Select all operating systems that apply to this issue"
      multiple: true
      options:
        - Linux
        - Windows
        - macOS (darwin)
        - Android
        - OpenBSD / FreeBSD

  - type: dropdown
    id: arch
    attributes:
      label: Architecture
      description: "Select all architectures that apply to this issue"
      multiple: true
      options:
        - amd64
        - amd64-v3
        - arm64
        - "386"
        - armv5
        - armv6
        - armv7
        - mips-softfloat
        - mips-hardfloat
        - mipsle-softfloat
        - mipsle-hardfloat
        - mips64
        - mips64le
        - riscv64

  - type: markdown
    attributes:
      value: "## Clash related information"
  - type: markdown
    attributes:
      value: |
        Please provide relevant information about your Clash instance here. If you
        do not provide enough information, the issue may be closed.

  - type: textarea
    attributes:
      render: YAML
      label: Configuration File
      placeholder: "Ensure that there is no sensitive information (such as server addresses, passwords, or ports) in the configuration file, and provide the minimum reproducible configuration. Do not post configurations with thousands of lines."
    validations:
      required: true

  - type: textarea
    attributes:
      render: Text
      label: Log
      placeholder: "Please attach the corresponding core outout (setting `log-level: debug` in the configuration provides debugging information)."

  - type: textarea
    attributes:
      label: Description
      placeholder: "Please describe your issue in detail here to help us understand (supports Markdown syntax)."
    validations:
      required: true

  - type: textarea
    attributes:
      label: Reproduction Steps
      placeholder: "Please provide the specific steps to reproduce the issue here (supports Markdown syntax)."

