name: Release
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          check-latest: true
          go-version: '1.21'

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3

      - name: Cache go module
        uses: actions/cache@v3
        with:
          path: |
            ~/go/pkg/mod
            ~/.cache/go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Build
        if: startsWith(github.ref, 'refs/tags/')
        env:
          NAME: clash
          BINDIR: bin
        run: make -j $(go run ./test/main.go) releases

      - name: Upload Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: bin/*
          draft: true
