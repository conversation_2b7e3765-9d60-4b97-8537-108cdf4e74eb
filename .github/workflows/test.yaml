name: Test

on: [push, pull_request]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          check-latest: true
          go-version: '1.21'

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3

      - name: Cache go module
        uses: actions/cache@v3
        with:
          path: |
            ~/go/pkg/mod
            ~/.cache/go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Get dependencies, run test
        run: |
          go test ./...

  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    steps:
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          check-latest: true
          go-version: '1.21'

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3

      - name: Cache go module
        uses: actions/cache@v3
        with:
          path: |
            ~/go/pkg/mod
            ~/.cache/go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Build
        env:
          NAME: clash
          BINDIR: bin
        run: make -j $(go run ./test/main.go) all
