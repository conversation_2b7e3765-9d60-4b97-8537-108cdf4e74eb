package hook

import (
	C "clash-foss/constant"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/tunnel"
	"context"
	"net/http"
	"strings"
)

func removeIndex(s []interface{}, index int) []interface{} {
	return append(s[:index], s[index+1:]...)
}

// config/config.go:260
func parseProxies(groups *[]map[string]interface{}, proxies *[]map[string]interface{}) {
	allConfig := data.GetAllProxyConfig()

	cSize := len(*allConfig)

	var defaultName = "-"
	var configLists []map[string]interface{}
	var nameLists []interface{}

	if cSize > 1 {
		configLists = make([]map[string]interface{}, cSize)
		nameLists = make([]interface{}, cSize)

		i := 0
		for _, c := range *allConfig {
			name := c["name"]
			nameLists[i] = name
			configLists[i] = c
			i += 1
		}

		*proxies = append(*proxies, configLists...)
	} else {
		if 1 > cSize && len(*proxies) > 0 {
			defaultName = (*proxies)[0]["name"].(string)
		}
	}

	for vindex := range *groups {
		oList := (*groups)[vindex]["proxies"].([]interface{})
		oSize := len(oList)

		//填充动态加载的代理
		if 5 > oSize {
			skip := true
			for k, m := range oList {
				v := m.(string)
				if v == "*" {
					skip = false
					oList = removeIndex(oList, k)
				}
			}
			if skip {
				continue
			}

			if 1 > len(oList) {
				oList = []interface{}{defaultName}
			}

			//如果数据已经更改过
			(*groups)[vindex]["proxies"] = oList
		}

		//如果代理池为空则
		if 1 > cSize {
			continue
		}

		if 1 == oSize {
			(*groups)[vindex]["proxies"] = nameLists
		} else {
			(*groups)[vindex]["proxies"] = append((*groups)[vindex]["proxies"].([]interface{}), nameLists...)
		}
	}
}

// tunnel/tunnel.go:163
func fastChoose(proxies *map[string]C.Proxy) C.Proxy {
	if proxy, ok := (*proxies)["AutoFast"]; ok {
		return proxy
	}
	return nil
}

// adapters/provider/healthcheck.go:50
func HealthCheckAll(proxies []C.Proxy, urls ...string) {
	batchSize := 10
	//if nil == proxies {
	proxies = tunnel.LoadOnlyProxies()
	//}
	if nil == proxies {
		return
	}
	pSize := len(proxies)
	testUrl := envs.DefaultURLTestURL

	if 1 > pSize {
		return
	}

	if len(urls) > 0 {
		testUrl = urls[0]
	}

	for pSize > 0 {
		ctx, cancel := context.WithTimeout(context.Background(), envs.DefaultFastURLTestTimeout)
		for j := 0; batchSize > j; j++ {
			pSize -= 1
			if 0 > pSize {
				break
			}
			proxy := proxies[pSize]
			go proxy.URLTest(ctx, testUrl)
		}

		<-ctx.Done()
		cancel()
	}
}

// adapters/outbound/base.go:169
func newRequest(url string) (*http.Request, error) {
	if strings.HasSuffix(url, "#") || strings.Contains(url, "youtube") {
		//flag := "?"
		//if strings.Contains(url, "?") {
		//	flag = "&"
		//}
		//url = strings.Replace(url, "#", fmt.Sprintf("%s_t=%d", flag, time.Now().Unix()), 1)
		return http.NewRequest(http.MethodGet, url, nil)
	}
	return http.NewRequest(http.MethodHead, url, nil)
}

/*
func ParseGroups(groups *[]map[string]interface{}, ps *map[string]C.Proxy, pls *[]string) {
	newProxies := holder.LoadConfigedProxy()

	size := len(*newProxies)

	if nil == newProxies || 1 > size {
		return
	}
	skip := false

	plists := make([]string, size)

	for j, _ := range *groups {

		//gps := mapping["proxies"].([]string)
		nps := make([]interface{}, size)

		for i, np := range *newProxies {
			name := np.Name()
			nps[i] = name

			if !skip {
				(*ps)[name] = np
				plists[i] = name
			}
		}

		(*groups)[j]["proxies"] = append((*groups)[j]["proxies"].([]interface{}), nps...)

		if !skip {
			skip = true
			*pls = append(*pls, plists...)
		}
	}

}
// */
