package hook

import (
	C "clash-foss/constant"
	"clash-foss/hooker"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/system"
	"net/http"
)

type Hooker struct {
}

func (this *<PERSON>) NewRequest(url string) (*http.Request, error) {
	return newRequest(url)
}
func (this *Hooker) IsMobile() bool {
	return envs.UseInMobile
}
func (this *Hooker) LoadConfigedProxy(proxies *[]C.Proxy) []C.Proxy {
	skip := true
	if nil != proxies && len(*proxies) > 0 {
		for _, v := range *proxies {
			name := v.Name()
			if 2 > len(name) && ("*" == name || "-" == name) {
				skip = false
				break
			}
		}
	}
	if skip {
		return nil
	}

	return system.GetAllProxies()
}
func (this *Hooker) GetAliveProxy() interface{} {
	return system.GetAliveProxy()
}
func (this *<PERSON>) HealthCheckAll(proxies []C.Proxy, urls ...string) {
	HealthCheckAll(proxies, urls...)
}
func (this *Hooker) RegisterRawConfig(cfg []byte) {
	data.RawConfigSetup(cfg)
}
func (this *Hooker) ParseProxies(groups *[]map[string]interface{}, proxies *[]map[string]interface{}) {
	//parseProxies(groups, proxies)
}
func (this *Hooker) FastChoose(proxies *map[string]C.Proxy) C.Proxy {
	return fastChoose(proxies)
}
func (this *Hooker) GetFastProxy() interface{} {
	return system.GetFastProxies()
}
func RegistToSystem() {
	if nil == hooker.Hooker {
		h := &Hooker{}
		hooker.Hooker = h
	}
}
