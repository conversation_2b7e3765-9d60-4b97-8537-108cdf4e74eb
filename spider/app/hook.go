package main

/*
import (
	"fmt"
	"clash-foss/adapter"
	C "clash-foss/constant"
	"clash-foss/hooker"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/system"
	"net/http"
)

var (
	proxy   C.Proxy
	xconfig map[string]interface{}
)

type Hooker struct {
}

func (this *<PERSON>) NewRequest(url string) (*http.Request, error) {
	//fmt.Println("====NewRequest==>")
	return nil, nil
}
func (this *Hooker) IsMobile() bool {
	return envs.UseInMobile
}
func (this *Hooker) GetAliveProxy() interface{} {
	//fmt.Println("====GetAliveProxy==>")
	return system.GetAliveProxy()
}
func (this *Hooker) GetFastProxy() interface{}{
	return system.GetAliveProxy()
}
func (this *Hooker) HealthCheckAll(proxies *[]C.Proxy, urls ...string) {
	//fmt.Println("====HealthCheckAll==>")

}
func (this *Hooker) GetAllProxies() []C.Proxy {
	return system.GetAllProxies()
}
func (this *<PERSON>) RegisterRawConfig(cfg []byte) {
	data.RawConfigSetup(cfg)
}
func (this *Hooker) ParseProxies(groups *[]map[string]interface{}, proxies *[]map[string]interface{}) {

	tmp1 := map[string]interface{}{
		"name":      "fast",
		"type":      "url-test",
		"proxies":   []string{proxy.Name()},
		"url":       "https://www.google.com/#",
		"interval":  300,
		"tolerance": 150,
	}
	*groups = append(*groups, tmp1)
	*proxies = append(*proxies, xconfig)

	fmt.Println("====ParseProxies==>")
}
func (this *Hooker) FastChoose(proxies *map[string]C.Proxy) C.Proxy {
	//fmt.Println("====FastChoose==>")
	//return fastChoose(proxies)
	return proxy
}

func RegistToSystem() {
	if nil == hooker.Hooker {
		h := &Hooker{}
		hooker.Hooker = h
	}

	server := "35a5eac0a97599169574f594e8b0e4d3.gc-gb-lhr-02-02lxzgeu.adguard.io"
	port := 443

	//xconfig := map[string]interface{}{
	//	"name":             fmt.Sprintf("%s-%d", server, port),
	//	"type":             "http",
	//	"server":           server,
	//	"port":             port,
	//	"tls":              true,
	//	"skip-cert-verify": true,
	//}

	xconfig = map[string]interface{}{
		"name":             fmt.Sprintf("%s-%d", server, port),
		"type":             "http",
		"server":           server,
		"port":             port,
		"tls":              true,
		"skip-cert-verify": true,
	}

	var err error
	proxy, err = adapter.ParseProxy(xconfig)

	if err != nil {
		fmt.Println( err)
	}
	fmt.Println("===RegistToSystem==>", err)
}
// */
