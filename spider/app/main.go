package main

/*
import (
	"fmt"
	"clash-foss/android/tun"
	"clash-foss/hub/executor"
	"clash-foss/log"
	"clash-foss/tunnel"
	"time"
	"clash-foss/config"
)
import 	P "clash-foss/proxy"

var (
	OptionalDnsPatch  *config.RawDNS
	DnsPatch          *config.RawDNS
	NameServersAppend []string

	//cachedPool *fakeip.Pool
)
func main() {
	test()
}

func LoadDefault() {
	DnsPatch = nil
	NameServersAppend = make([]string, 0)

	defaultC, err := config.Parse([]byte{})
	if err != nil {
		log.Warnln("Load Default Failure " + err.Error())
		return
	}
	defaultC.General.Mode = tunnel.Rule


	executor.ApplyConfig(defaultC, true)

	tun.InitialResolver()
}

func test()  {

	port := 8866

	log.SetLevel(log.DEBUG)

	P.SetAllowLan(true)

	RegistToSystem()

	LoadDefault()

	tunnel.SetMode(tunnel.Rule)

	P.SetBindAddress("127.0.0.1")

	//P.ReCreateHTTP(pointerOrDefault(general.Port, ports.Port))
	err := P.ReCreateSocks(port)
	//P.ReCreateRedir(pointerOrDefault(general.RedirPort, ports.RedirPort))
	//P.ReCreateMixed(pointerOrDefault(general.MixedPort, ports.MixedPort))

	fmt.Println("OK", err)




	for {
		time.Sleep(10 * time.Second)
		fmt.Println("OK")
	}
}

// */
