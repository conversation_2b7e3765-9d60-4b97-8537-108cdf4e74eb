
flags='-ldflags=\"-s -w\"'
# flags=""
BUILDTIME=`date +"%y%m%d%H"`

#export GOPROXY="https://mirrors.aliyun.com/goproxy/"
export GOARM=7
# gomobile init
# gomobile bind -v -target=android/arm $flags -trimpath miniSsr/jni
# gomobile bind -target=android/arm $flags -trimpath github.com/Dreamacro/clash/spider/roxyp


gomobile bind -target=android/arm -ldflags="-s -w -X \"github.com/Dreamacro/clash/spider/envs.Build=$BUILDTIME\"" -trimpath github.com/Dreamacro/clash/spider/roxyp


# export GOARM=8
# gomobile bind -target=android/arm64 -trimpath github.com/kr328/cfa/bridge


# dest="/Volumes/VA/data/android/Tester/app/libs"
# cp -frv *.aar $dest/roxyp-`date "+%s"`.aar


dest="/Volumes/VA/data/android/wings/xypro/libs"
rm -frv $dest/roxyp*
cp -frv *.aar $dest/