package spider

import (
	"clash-foss/log"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/hook"
	"clash-foss/spider/manager"
	"fmt"
	"sync"
	"time"
)

var (
	locker sync.Mutex

	isServiceStart = false
	AddFinish      = envs.AddFinish
	NeedUp         = false
	StartWatchOnce = manager.StartWatchOnce
)

func ISRunning() bool {
	return isServiceStart
}

func StartAutoProxyServices() {
	locker.Lock()
	defer locker.Unlock()

	if isServiceStart {
		return
	}
	if NeedUp {
		return
	}

	isServiceStart = true
	time.Sleep(3 * time.Second)
	hook.RegistToSystem()
	data.Subscribe()
	log.Debugln(envs.Build)
	if envs.UseInMobile {
		log.SetLevel(log.ERROR)
	}
	manager.AllServiceStart()

	AddFinish(func() {
		//就算结束也不重置，意味着此方法只能执行一次
		//isServiceStart = false
		//加载最新代理
		envs.Reload()
		manager.Save()
	})
}

func MobileInit() {
	envs.ResloveName = 1
	envs.UseInMobile = true
	envs.ShowSatus = false
	envs.Debug = false
	log.SetLevel(log.ERROR)
}

func AddRoot(root string) {
	envs.AddRoot(root)
	envs.UseHttpProxy = true
	envs.IsNeedUpdate()
	NeedUp = envs.ServiceEnabled == false
	hook.RegistToSystem()
}

func GetTotal() int {
	return envs.Total
}

func GetScaning() bool {
	if isServiceStart {
		return isServiceStart
	}
	return manager.GetJobCount() > 0
}

func AddWatch(vtype, url string) {
	AddUWatch(fmt.Sprintf("%s@%s", vtype, url))
}
func AddUWatch(url string) {
	//data.WatchList = append(data.WatchList, url)
	data.AddWatch(url)
}

//func AddExtenHttp(fn func(string) ([]byte, error))  {
//	manager.ExtHttpGet = fn
//}

//func Rpc(a int, uri string)string  {
//
//}
