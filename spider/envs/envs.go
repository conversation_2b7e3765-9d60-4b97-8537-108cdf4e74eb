package envs

import (
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"strconv"
	"strings"
	"time"
)

var (
	Version         = 5
	ServiceEnabled  = true
	UseInMobile     = false
	ISNetworkConned = true
	Root            = ""         //操作目录
	ConfigFile      = ""         //配置文件
	AppId           = ""         //根据root自动加载包名
	Build           = "20220508" //编译时自动添加
	UMes            = ""         //前端广告
	IsChinese       = true       //是否中文

	Total = 0

	Debug = false

	//不监视proxy数量
	NoWatch = false

	ShowSatus = true

	//服务器的地区名字
	ResloveName = 0

	//IP/地区名方法
	ResloveNameFunc func(string) string = nil

	//使用内置代理来读取http
	UseHttpProxy = true

	FinishCallback = make([]func(), 0)

	DefaultURLTestTimeout     = time.Second * 10
	DefaultHttpTimeout        = time.Second * 5
	DefaultConnectTimeout     = time.Second * 5
	DefaultFastURLTestTimeout = time.Second * 5

	//多少分钟扫描一次代理
	TaskIntervalMinute = 30

	//多少秒检查一次代理连通性
	CheckIntervalSec = 600

	//测试连续失败多少次后删除代理
	ProxyCleanerTimes = 5

	//搜索代理的工作线程
	WatchWorkerNum = 8

	//扫描器数量
	//NumberScanner = 20

	//共享任务处理器数量
	NumberShareWd = 30

	DefaultURLTestURL = "http://www.google.com#"

	//優先加載的配置
	ExtendConfig []byte = nil

	//默認配置加載器
	DefaultReLoader func()

	//是否快速测试端口可用性
	FastPing = true

	//每次启动时暫緩加載代理
	//而是先檢查是否連通
	LoadAfterChecked = true

	//最大可管理数量
	MaxMngerProxy = 100

	//每个IP允许有多个代理【不同端口】
	AllowMultiHostOneIp = false
)

func AddFinish(fn func()) {
	FinishCallback = append(FinishCallback, fn)
}

func IsNeedUpdate(ends ...bool) bool {

	fn := path.Join(Root, fmt.Sprintf("desp.%d", Version))

	if len(ends) > 0 && ends[0] {
		ioutil.WriteFile(fn, []byte("1"), 0666)
		return true
	}

	if _, err := os.Stat(fn); os.IsNotExist(err) {
		ServiceEnabled = true
		return false
	} else {
		ServiceEnabled = false
	}
	return true

}
func AddRoot(path string) {
	Root = path
	if len(path) > 10 && strings.HasPrefix(path, "/data") {
		tmp := strings.Split(path, "/")
		for _, t := range tmp {
			if strings.Count(t, ".") > 1 {
				AppId = t
			}
		}
	}

}

func init() {
	if len(Build) > 2 {
		c, err := strconv.Atoi(Build)
		if err == nil && c > 100 {
			Version = c*100 + Version
		}
	}
}

func Reload() {
	if nil != DefaultReLoader {
		DefaultReLoader()
	}
}

func IsFull() bool {
	return Total >= MaxMngerProxy
}
