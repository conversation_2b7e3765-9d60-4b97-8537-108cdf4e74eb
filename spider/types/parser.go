package types

import (
	"clash-foss/log"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
)

type HashMap map[string]interface{}

func (this *HashMap) Add(key string, v interface{}) {
	if nil == v {
		return
	}
	switch v.(type) {
	case string:
		if "" == v {
			return
		}
	}
	(*this)[key] = v
}

func decodeSSR(ssr string) string {

	m := strings.Split(ssr, "://")

	return m[0] + "://" + Base64d(m[1])
}
func ConvInt(str string) (m int) {
	var err error
	m, err = strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return
}

func ParseSSR(uri string) map[string]interface{} {
	defer func() {
		if err := recover(); err != nil {
			//log/("panic recover", err)
			return
		}
	}()

	//fmt.Println("======>", uri)

	if strings.HasPrefix(uri, "vmess") {
		return parseVmess(uri)
	}

	config := strings.Split(uri, "://")

	uri = Base64d(config[1])

	//第二种格式
	//aes-256-cfb:9d6cceaa373bf2c8acb22e60b6a58be6@*************:443
	atFlag := strings.Contains(uri, "@")

	param := strings.FieldsFunc(uri, func(r rune) bool {
		return r == ':' || r == '@'
	})

	item := map[string]interface{}{
		"server": param[0],
		"port":   ConvInt(param[1]),
		"type":   config[0],
	}

	item["_uri"] = uri

	if 4 > len(param) {
		return nil
	}

	switch config[0] {
	case "ss":
		item["cipher"] = param[2]
		item["password"] = param[3]

		//第二种格式
		if atFlag {
			item["cipher"] = param[0]
			item["password"] = param[1]
			item["server"] = param[2]
			item["port"] = ConvInt(param[3])
		}
	case "ssr":

		if strings.Contains(param[5], "?") {
			config = strings.Split(param[5], "?")
			u, _ := url.ParseQuery(config[1])
			//fmt.Printf("== %#v\n", u)
			item["password"] = Base64d(strings.Trim(config[0], "/"))

			//item["obfsparam"] = u.Get("obfsparam")
			//item["protocolparam"] = u.Get("protoparam")

			item["obfsparam"] = Base64d(u.Get("obfsparam"))
			item["protocolparam"] = Base64d(u.Get("protoparam"))
		}

		item["protocol"] = param[2]
		item["cipher"] = param[3]
		item["obfs"] = param[4]

	}

	////TODO 只是测试
	if "rc4" == item["cipher"] {
		item["cipher"] = "rc4-md5"
	}

	if "none" == item["cipher"] {
		item["cipher"] = ""
	}

	item["name"] = fmt.Sprintf("%s_%s_%d", item["type"], item["server"], item["port"])

	//https://github.com/CheerChen/sub2clashr/blob/bfb9b26f1befa22de0e1cd954c508c90f73ee636/clash/vpn_ssr.go
	//if "origin" == item["protocol"] && "plain" == item["obfs"] {
	//	switch item["cipher"] {
	//	case "aes-128-gcm", "aes-192-gcm", "aes-256-gcm",
	//		"aes-128-cfb", "aes-192-cfb", "aes-256-cfb",
	//		"aes-128-ctr", "aes-192-ctr", "aes-256-ctr",
	//		"rc4-md5", "chacha20", "chacha20-ietf", "xchacha20",
	//		"chacha20-ietf-poly1305", "xchacha20-ietf-poly1305":
	//		item["type"] = "ss"
	//	}
	//}

	return item

}

func Base64d(str string) string {

	if "" == str || 2 > len(str) {
		return str
	}

	if strings.Contains(str, ":") ||
		strings.Contains(str, "-") {
		return str
	}

	oldStr := str

	if strings.Contains(str, "+") {
		deStr, err := base64.StdEncoding.DecodeString(str)
		if err == nil {
			return string(deStr)
		}
	}

	deStr, err := base64.URLEncoding.DecodeString(str)

	if err == nil {
		return string(deStr)
	}

	for i := 0; i <= len(str)%4; i++ {
		str += "="
	}

	deStr, err = base64.URLEncoding.DecodeString(str)

	if err != nil {
		return oldStr
	}

	return string(deStr)
}

// chacha20-poly1305:<EMAIL>:443
// network=tcp&aid=0&tls=0&allowInsecure=1&mux=0&muxConcurrency=8&remark=TCP%20Test%20Outbound
func parseVmess(config string) map[string]interface{} {
	var server string

	iq := strings.Index(config, "?")

	item := map[string]interface{}{
		"type": "vmess",
	}

	cons := ConUri{}
	item["_uri0"] = config

	if iq > 1 {
		if err := cons.Init(config); nil != err {
			server = config[8:iq]
			//return item
		}

		u := cons.Query()
		tls := u.Get("tls")
		udp := u.Get("udp")
		wsPath := u.Get("wsPath")
		tlsServer := u.Get("tlsServer")

		//fmt.Printf("== %#v\n", u, server)
		item["alterId"] = ConvInt(u.Get("aid"))
		item["network"] = u.Get("network")
		item["tls"] = false
		item["udp"] = false
		item["skip-cert-verify"] = true

		if "1" == tls || "true" == tls {
			item["tls"] = true
		}
		if "1" == udp || "true" == udp {
			item["udp"] = true
		}

		if len(wsPath) > 0 {
			item["ws-path"] = wsPath
		}

		if len(tlsServer) > 0 {
			item["servername"] = tlsServer
		}

	} else {
		server = config[8:]
	}

	server = Base64d(server)
	
	//第二种格式
	if strings.HasPrefix(server, "{") {
		return parseVmessJsonV1(server)
	}

	item["cipher"] = cons.User
	item["uuid"] = cons.Pwd
	item["server"] = cons.Host
	item["port"] = ConvInt(cons.Port)
	item["name"] = fmt.Sprintf("vmess_%s_%d", item["server"], item["port"])

	return item
}
func parseXInt(v interface{}) int {
	switch v.(type) {
	case int:
		return v.(int)
	case string:
		return ConvInt(v.(string))
	case float64:
		return int(v.(float64))
	case float32:
		return int(v.(float32))
	case int64:
		return int(v.(int64))
	}
	return 0
}
func parseXString(v interface{}) string {
	switch v.(type) {
	case int:
		return strconv.Itoa(v.(int))
	case string:
		return v.(string)
	case float64:
		return strconv.Itoa(int(v.(float64)))
	case float32:
		return strconv.Itoa(int(v.(float32)))
	}
	return ""
}

// https://github1s.com/v2fly/vmessping/blob/HEAD/vmess/vmess.go
func parseVmessJsonV1(config string) map[string]interface{} {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(config), &data)
	if err != nil {
		log.Warnln("parseVmessJsonV1", err.Error())
		return nil
	}
	item := map[string]interface{}{
		"type": "vmess",
	}

	tls := data["tls"]
	udp := data["udp"]
	wsPath := parseXString(data["path"])
	tlsServer := parseXString(data["host"])
	cipher := parseXString(data["type"])

	item["alterId"] = parseXInt(data["aid"])
	item["network"] = data["net"]
	item["tls"] = false
	item["udp"] = false
	item["skip-cert-verify"] = true

	if "1" == tls || "true" == tls {
		item["tls"] = true
	}
	if "1" == udp || "true" == udp {
		item["udp"] = true
	}

	if len(wsPath) > 0 {
		item["ws-path"] = wsPath
	}

	if len(tlsServer) > 0 {
		item["servername"] = tlsServer
	}

	if 1 > len(cipher) {
		cipher = "auto"
	}

	item["cipher"] = cipher
	item["uuid"] = data["id"]
	item["server"] = data["add"]
	item["port"] = parseXInt(data["port"])
	item["name"] = fmt.Sprintf("vmess_%s_%d", item["server"], item["port"])

	return item
}

func ParseSurge(v string) map[string]interface{} {

	v = strings.Replace(v, ",", "&", -1)
	v = strings.Replace(v, " ", "", -1)

	cuts := strings.FieldsFunc(v, func(r rune) bool {
		return '&' == r || '=' == r
	})

	u, _ := url.ParseQuery(v)

	item := HashMap{
		"type": cuts[1],
	}

	switch cuts[1] {
	case "ss":
		item["server"] = cuts[2]
		item["port"] = ConvInt(cuts[3])
		item.Add("cipher", u.Get("encrypt-method"))
		item.Add("obfs", u.Get("obfs"))
		item.Add("obfs-host", u.Get("obfs-host"))
		item.Add("password", u.Get("password"))
	case "vmess":
		item["server"] = cuts[2]
		item["port"] = ConvInt(cuts[3])
		item["uuid"] = u.Get("username")
		tls := u.Get("tls")
		isws := u.Get("ws")

		item["tls"] = false
		item["udp"] = false
		item["alterId"] = 0
		item["skip-cert-verify"] = true

		if "1" == isws || "true" == isws {
			item["network"] = "ws"
			item.Add("ws-path", u.Get("ws-path"))
			item.Add("servername", u.Get("sni"))
		}

		if "1" == tls || "true" == tls {
			item["tls"] = true
		}

	case "trojan":
		item["server"] = cuts[2]
		item["port"] = ConvInt(cuts[3])
		item["password"] = cuts[5]
		item["sni"] = cuts[7]

	default:
		log.Warnln("ParseSurge %s", cuts[1])
	}

	item["name"] = fmt.Sprintf("vmess_%s_%d", item["server"], item["port"])

	return item
}

func ParseSSRV2(uri string) map[string]interface{} {
	defer func() {
		if err := recover(); err != nil {
			//log/("panic recover", err)
			return
		}
	}()

	if strings.HasPrefix(uri, "vmess") {
		return parseVmess(uri)
	}

	uuu := ConUri{}
	if err := uuu.Init(uri); nil != err {
		fmt.Println("========Parse-ERROR=========>")
		fmt.Println(uri)
		return ParseSSR(uri)
	}

	item := map[string]interface{}{
		"server": uuu.Host,
		"port":   ConvInt(uuu.Port),
		"type":   uuu.Uri.Scheme,
	}

	item["_uri"] = fmt.Sprintf("%s://%s@%s?%s", uuu.Uri.Scheme, uuu.Pwd, uuu.Host, uuu.Uri.RawQuery)
	item["_uri0"] = uri

	qv := uuu.Query()
	for k := range qv {
		item[k] = qv.Get(k)
	}

	switch uuu.Uri.Scheme {
	case "ss":
		item["cipher"] = uuu.User
		item["password"] = uuu.Pwd
	case "ssr":

		//item["obfsparam"] = u.Get("obfsparam")
		//item["protocolparam"] = u.Get("protoparam")

		if t, ok := qv["obfsparam"]; ok {
			item["obfsparam"] = Base64d(t[0])
		}
		if t, ok := qv["protocolparam"]; ok {
			item["protocolparam"] = Base64d(t[0])
		}

	}

	////TODO 只是测试
	if "rc4" == item["cipher"] {
		item["cipher"] = "rc4-md5"
	}

	if "none" == item["cipher"] {
		item["cipher"] = ""
	}

	item["name"] = fmt.Sprintf("%s_%s_%d", item["type"], item["server"], item["port"])

	//https://github.com/CheerChen/sub2clashr/blob/bfb9b26f1befa22de0e1cd954c508c90f73ee636/clash/vpn_ssr.go
	//if "origin" == item["protocol"] && "plain" == item["obfs"] {
	//	switch item["cipher"] {
	//	case "aes-128-gcm", "aes-192-gcm", "aes-256-gcm",
	//		"aes-128-cfb", "aes-192-cfb", "aes-256-cfb",
	//		"aes-128-ctr", "aes-192-ctr", "aes-256-ctr",
	//		"rc4-md5", "chacha20", "chacha20-ietf", "xchacha20",
	//		"chacha20-ietf-poly1305", "xchacha20-ietf-poly1305":
	//		item["type"] = "ss"
	//	}
	//}

	return item

}

type ConUri struct {
	Uri  *url.URL
	Host string
	Port string
	User string
	Pwd  string
}

func (this *ConUri) Init(uri string) error {
	var err error
	this.Uri, err = url.Parse(uri)
	if err != nil {
		return err
	}

	if strings.Contains(this.Uri.Host, ":") {
		host := strings.Split(this.Uri.Host, ":")
		this.Host = host[0]
		this.Port = host[1]
	}
	this.User = Base64d(this.Uri.User.Username())
	if b, ok := this.Uri.User.Password(); ok {
		this.Pwd = Base64d(b)
	}

	return nil
}
func (this *ConUri) Query() url.Values {
	return this.Uri.Query()
}
