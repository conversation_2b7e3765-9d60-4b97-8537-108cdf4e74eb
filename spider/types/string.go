package types

import (
	"encoding/base64"
	"log"
)

var (
	KEYS = []byte{
		186, 174, 228, 185, 139, 228, 184, 138, 230, 156, 137, 229, 164, 154, 229, 176,
		230, 136, 145, 229, 156, 168, 228, 187, 176, 230, 156, 155, 230, 156, 136, 228,
		231, 154, 132, 233, 163, 158, 231, 191, 148, 230, 152, 168, 229, 164, 169, 233,
		145, 230, 162, 166, 230, 131, 179, 229, 156, 168, 232, 135, 170, 231, 148, 177,
		134, 229, 191, 167, 228, 188, 164, 230, 136, 145, 232, 166, 129, 229, 146, 140,
		129, 151, 229, 191, 152, 229, 149, 138, 233, 163, 142, 229, 185, 178, 228, 186,
		139, 141, 232, 140, 171, 231, 154, 132, 232, 183, 175, 228, 184, 138, 231, 148,
		228, 189, 160, 233, 135, 141, 233, 128, 162, 229, 156, 168, 233, 130, 163, 232,
		230, 189, 174, 232, 144, 189, 230, 189, 174, 230, 182, 168, 230, 156, 137, 228,
		159, 229, 145, 189, 229, 183, 178, 232, 162, 171, 231, 137, 181, 229, 188, 149,
		175, 229, 164, 169, 229, 160, 130, 230, 136, 145, 231, 173, 137, 229, 190, 133,
		189, 160, 231, 154, 132, 232, 191, 156, 230, 150, 185, 229, 176, 177, 230, 152,
		231, 129, 181, 233, 173, 130, 230, 151, 169, 229, 183, 178, 232, 132, 177, 229,
		230, 136, 145, 230, 131, 179, 232, 177, 161, 32, 230, 136, 145, 231, 154, 132,
		172, 232, 185, 132, 229, 163, 176, 232, 144, 189, 111, 104, 121, 101, 97, 104,
		131, 181, 233, 169, 172, 232, 185, 132, 229, 163, 176, 232, 181, 183, 233, 169,
	}
)

func DecryptBase64(s string) string {
	data, err := base64.URLEncoding.DecodeString(s)
	if err != nil {
		log.Println(err)
	}

	if nil != data {
		Decrypt(data, 0)
		return string(data)
	}

	return ""

}
func EncryptBase64(bytes []byte) string {
	if nil != bytes {
		Decrypt(bytes, 0)
	}

	return base64.URLEncoding.EncodeToString(bytes)

}
func Decrypt(bytes []byte, length int) {
	var offset int

	if nil == bytes {
		return
	}

	totalOffset := len(bytes)

	if 1 > totalOffset {
		return
	}

	if 1 > length || length > totalOffset {
		length = totalOffset
	}
	if 10 > length {
		length = totalOffset / length
	}
	for i := 0; i < length; i++ {
		offset = i
		if i > 0x7FFF {
			offset = i % 0x7FFF
		}
		offset = (offset*offset + 0x13C1B) & 0xFF
		mask := KEYS[offset]

		bytes[i] = bytes[i] ^ mask
	}
}
