package types

type ExWatch struct {
	Type   string `yaml:"type"`
	Url    string `yaml:"url"`
	Ext    string `yaml:"ext"`
	Force  bool   `yaml:"force"`
	Status int    `yaml:"-"`
}

type ExGeneralConfig struct {
	Checker []map[string]string `yaml:"checker"`
	Watch   []ExWatch           `yaml:"watch"`
	SSR     []string            `yaml:"ssr"`
}

//type IExConfig interface {
//	Test() []string
//}
//
//func (this *ExGeneralConfig) Test() []string {
//	return this.SSRTest
//}
