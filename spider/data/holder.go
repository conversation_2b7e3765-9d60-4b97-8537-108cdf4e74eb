package data

import (
	"clash-foss/log"
	"clash-foss/spider/cache"
	"clash-foss/spider/envs"
	"clash-foss/spider/misc"
	"clash-foss/spider/types"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"path"
	"strconv"
	"strings"
	"sync"
)

var (
	fnFlag                            = "a.db"
	fnConfig                          = "d.db"
	configChange                      = 0
	startPxyCount                     = 0
	isConfigLoaded                    = false
	isFlagLoaded                      = false
	configFristReload                 = false
	rawxConfig         []byte         = nil
	Status                            = XStatus{}
	WatchList                         = make([]string, 0)
	cacheExpflag       *cache.GoCache = nil
	stores             *StoreConfig   = nil
	unCheckedStores    *StoreConfig   = nil
	storeDbLocker      sync.Mutex
	CheckStoreFunc     func(stores *StoreConfig) = nil
	cacheStore         *cache.GoCache            = nil
	tmpNamecLocker     sync.Mutex
	CountryResloveFunc func(string) string
)

func init() {
	stores = NewStoreConfig()
	if envs.LoadAfterChecked {
		unCheckedStores = NewStoreConfig()
	}
	gob.Register(map[interface{}]interface{}{})
}

type XStatus struct {
	Good int
}

type StoreConfig struct {
	Vstop   int    //停用版本
	Mstop   string //停用原因
	Configs map[string]map[string]interface{}
	Names   map[string]int
}

func NewStoreConfig() *StoreConfig {
	return &StoreConfig{
		Configs: make(map[string]map[string]interface{}),
		Names:   make(map[string]int),
	}
}

type HashPrxConfig struct {
	Config map[string]interface{}
}

func (this *HashPrxConfig) ProxyId() string {
	if envs.AllowMultiHostOneIp {
		return this.Host()
	}
	return this.Server()
}
func (this *HashPrxConfig) Host() string {
	return fmt.Sprintf("%s:%d", this.Server(), this.Port())
}
func (this *HashPrxConfig) Server() string {
	return this.Config["server"].(string)
}
func (this *HashPrxConfig) Port() int {
	return this.Config["port"].(int)
}
func (this *HashPrxConfig) GenName() {
	name := ""
	host := this.Server()
	if envs.ResloveName > 0 {
		country := CountryResloveFunc(host)
		if len(country) > 1 {
			ext := host
			switch envs.ResloveName {
			case 1:
				tmpNamecLocker.Lock()
				nc, ok := stores.Names[country]
				if ok {
					nc += 1
				} else {
					nc = 1
				}
				stores.Names[country] = nc
				ext = strconv.Itoa(nc)
				tmpNamecLocker.Unlock()
				break
			case 2:
				break
			case 3:
				ext = fmt.Sprintf("%s-%d", host, this.Port())
				break
			}

			name = fmt.Sprintf("%s[%s]", country, ext)
		}
	}

	if 2 > len(name) {
		name = fmt.Sprintf("%s-%d", host, this.Port())
	}

	this.Config["name"] = name
}

func RawConfig() []byte {
	return rawxConfig
}

func RawConfigSetup(cfg []byte) {
	rawxConfig = cfg
}

func AddWatch(url string) {
	WatchList = append(WatchList, url)
}

//	func IsProxyExist(pid string) bool {
//		if _, ok := configList[pid]; ok {
//			return true
//		}
//		return false
//	}
func HasConfig(hc *HashPrxConfig) bool {
	if _, ok := stores.Configs[hc.ProxyId()]; ok {
		return true
	}
	return false
}
func AddConfig(c *HashPrxConfig) {
	storeDbLocker.Lock()
	if HasConfig(c) {
		storeDbLocker.Unlock()
		return
	}
	if nil == cacheStore {
		LoadDiskCache()
	}
	configChange += 1
	stores.Configs[c.ProxyId()] = c.Config
	total := len(stores.Configs)
	Status.Good = total

	//加到第5個時自動重載一下
	//考慮到第一次運行全沒有可用配置
	if !configFristReload && total > 5 {
		configFristReload = true
		envs.Reload()
	}

	storeDbLocker.Unlock()
}

func RemoveConfig(names ...string) {
	storeDbLocker.Lock()
	for _, name := range names {
		configChange += 1
		delete(stores.Configs, name)
	}
	Status.Good = len(stores.Configs)
	storeDbLocker.Unlock()
}

func GetAllProxyConfig() *map[string]map[string]interface{} {
	LoadDiskCache()
	return &stores.Configs
}

func LoadDiskCache() {
	if 1 > len(envs.Root) {
		return
	}

	if !isFlagLoaded {
		isFlagLoaded = true
		cacheExpflag = cache.NewDataCache(path.Join(envs.Root, fnFlag))
	}

	if !isConfigLoaded {
		isConfigLoaded = true

		totalLoaded := 0
		fn := path.Join(envs.Root, fnConfig)
		cacheStore = cache.NewGoCache(fn)

		//需要先檢查一下是否暫緩加載
		if envs.LoadAfterChecked {
			cacheStore.Load(unCheckedStores)
			stores.Mstop = unCheckedStores.Mstop
			stores.Vstop = unCheckedStores.Vstop
			startPxyCount = len(unCheckedStores.Configs)
			totalLoaded = startPxyCount
		} else {
			cacheStore.Load(stores)
			totalLoaded = len(stores.Configs)
		}

		//如果此版本已经停用则返回
		if envs.Version == stores.Vstop {
			return
		}

		Status.Good = len(stores.Configs)

		if envs.Debug {
			log.Errorln("Load %d proxies from %s", totalLoaded, fn)
		}

		misc.Add1MinuteTask(func() {
			Save()
		})

		//檢查緩存的代理
		if envs.LoadAfterChecked && nil != CheckStoreFunc {
			go misc.SafeGo(func() {
				CheckStoreFunc(unCheckedStores)
			})
		}
	}
}
func Save() {
	if !envs.ISNetworkConned {
		return
	}

	cacheExpflag.AutoSave()

	if configChange > 3 {
		storeDbLocker.Lock()
		defer storeDbLocker.Unlock()

		c := len(stores.Configs)

		//如果上次保存的结果更多
		if 10 > c && startPxyCount > 10 {
			for k, v := range stores.Configs {
				unCheckedStores.Configs[k] = v
			}
			cacheStore.Save(unCheckedStores)
		} else {
			cacheStore.Save(stores)
		}
		configChange = 0
	}
}

func IsFlagExists(vid string) bool {
	return cacheExpflag.IsNotExp(vid)
}

func FlagAdd(vid string) {
	cacheExpflag.PutExp(vid, 3600*3)
}

func LogStop(v int, msg string) {
	stores.Vstop = v
	stores.Mstop = msg
	cacheStore.Save(stores)
}
func AddProxy(url string) {
	var config map[string]interface{}
	tmp := strings.Split(url, "://")
	switch tmp[0] {
	case "http", "https":
		host := strings.Split(tmp[1], ":")
		config = map[string]interface{}{
			"name":             fmt.Sprintf("%s-%s", host[0], host[1]),
			"type":             "http",
			"server":           host[0],
			"port":             types.ConvInt(host[1]),
			"tls":              true,
			"skip-cert-verify": true,
		}
		AddConfig(&HashPrxConfig{Config: config})
		break
	}
}

func JvConfig() []byte {
	storeDbLocker.Lock()
	defer storeDbLocker.Unlock()

	datas := make(map[string]map[string]interface{})
	if nil == stores || 5 > len(stores.Configs) {
		return nil
	}
	if nil != unCheckedStores {
		for k, v := range unCheckedStores.Configs {
			datas[k] = v
		}
	}

	if nil != stores {
		for k, v := range stores.Configs {
			datas[k] = v
		}
	}

	jsx, _ := json.Marshal(datas)
	return jsx
}
