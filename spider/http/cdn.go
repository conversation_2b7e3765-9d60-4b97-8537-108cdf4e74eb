package http

import (
	"fmt"
	"strings"
)

type CdnHelper struct {
	user   string
	repo   string
	branch string
	path   string
}

func CdnParse(url string) *CdnHelper {
	cdnHelper := CdnHelper{}
	return cdnHelper.doParse(url)
}

func (this *CdnHelper) Jsdelivr() string {
	branch := this.branch
	if 1 > len(branch) {
		branch = "master"
	}
	return fmt.Sprintf("https://cdn.jsdelivr.net/gh/%s/%s@%s%s", this.user, this.repo, branch, this.path)
}

func (this *CdnHelper) Sourcegraph() string {
	branch := this.branch
	if 1 > len(branch) {
		branch = "master"
	}
	return fmt.Sprintf("https://sourcegraph.com/github.com/%s/%s@%s/-/raw%s", this.user, this.repo, branch, this.path)
}

func (this *CdnHelper) doParse(url string) *CdnHelper {
	if strings.Contains(url, "jsdelivr") {
		sp1 := strings.Split(url, "/gh/")[1]
		sp2 := strings.Split(sp1, "/")
		this.user = sp2[0]
		repo := sp2[1]
		if strings.Contains(repo, "@") {
			sp3 := strings.Split(repo, "@")
			this.repo = sp3[0]
			this.branch = sp3[1]
		} else {
			this.repo = repo
		}
		this.path = strings.Split(url, repo)[1]
		return this
	}

	if strings.Contains(url, "github.com") {
		if strings.Contains(url, "/blob/") || strings.Contains(url, "/raw/") {
			sp1 := strings.Split(url, "com/")
			sp2 := strings.Split(sp1[1], "/")
			this.user = sp2[0]
			this.repo = sp2[1]
			this.branch = sp2[3]
			this.path = strings.Split(url, sp2[3])[1]
			return this
		}
	}

	if strings.Contains(url, "githubusercontent") {
		sp1 := strings.Split(url, "com/")
		sp2 := strings.Split(sp1[1], "/")
		this.user = sp2[0]
		this.repo = sp2[1]
		this.branch = sp2[2]
		this.path = strings.Split(url, sp2[2])[1]
		return this
	}

	return nil
}
