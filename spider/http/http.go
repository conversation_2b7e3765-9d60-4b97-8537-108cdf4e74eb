package http

import (
	"bytes"
	"clash-foss/adapter/provider"
	C "clash-foss/constant"
	"clash-foss/log"
	"clash-foss/spider/envs"
	"clash-foss/spider/misc"
	"clash-foss/spider/types"
	"clash-foss/tunnel"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

var (
	_base     = string([]byte("http://v1.goologo.com"))
	userAgent = fmt.Sprintf("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3100.0 Safari/%d", envs.Version)
	_http     = string([]byte("http"))
	_https    = string([]byte("https"))
	_igtm     = make(map[string]bool)
)

func CdnGet(reqUrl string) (data []byte) {
	cdn := CdnParse(reqUrl)
	if nil != cdn {
		cUrl := ""
		for i := 0; i < 2; i++ {
			switch i {
			case 0:
				cUrl = cdn.Jsdelivr()
				break
			default:
				cUrl = cdn.Sourcegraph()
				break
			}
			request := makeRequest(cUrl)
			if nil == request {
				continue
			}
			data = HttpGetDirect(request)
			if nil != data {
				return data
			}

			data = HttpGet(cUrl)
			if nil != data {
				return data
			}
		}
	}

	return HttpGet(reqUrl)
}

func makeRequest(reqUrl string) *http.Request {
	request, err := http.NewRequest(http.MethodGet, reqUrl, nil)
	request.Header.Set("User-Agent", userAgent)
	if err != nil {
		return nil
	}
	return request
}

func HttpGet(reqUrl string) (data []byte) {
	request, err := http.NewRequest(http.MethodGet, reqUrl, nil)
	request.Header.Set("User-Agent", userAgent)
	if err == nil {
		data = HttpGetWithProxy(request)
		if nil != data {
			return data
		}
	}

	if envs.UseHttpProxy {
		data = HttpGetWithVehicle(reqUrl)
		if nil != data {
			return data
		}
	}

	data = HttpGetDirect(request)
	if nil != data {
		return data
	}

	if !envs.UseHttpProxy {
		return HttpGetWithVehicle(reqUrl)
	}

	return nil
}

func HttpFetch(request *http.Request) (data []byte) {
	data = HttpGetWithProxy(request)
	if nil != data {
		return data
	}

	data = HttpGetDirect(request)
	if nil != data {
		return data
	}

	return HttpGetWithVehicle(request.URL.String())
}

func HttpGetDirect(reqest *http.Request) (data []byte) {
	//client := &http.Client{}
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr, Timeout: envs.DefaultURLTestTimeout}

	resp, err := client.Do(reqest)
	if err != nil {
		log.Infoln("http3 %s", err.Error())
		return nil
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)

	if err != nil {
		log.Infoln("http3 %s", err.Error())
	}

	return body
}
func HttpGetWithVehicle(reqUrl string) (data []byte) {
	vehicle := provider.NewHTTPVehicle(reqUrl, "")
	data, err := vehicle.Read()
	if err != nil {
		log.Infoln("http2 %s", err.Error())
		return nil
	}
	return data
}
func HttpGetWithProxy(req *http.Request) (data []byte) {
	proxies := tunnel.LoadSortedProxies(3)
	if nil == proxies || 1 > len(proxies) {
		return nil
	}
	data = nil
	var ctx context.Context
	var cancel context.CancelFunc

	for _, tmp := range proxies {
		if nil != cancel {
			cancel()
		}
		proxy := tmp.(C.Proxy)
		addr, err := urlToMetadata(req.URL.String())
		if err != nil {
			continue
		}

		ctx, cancel = context.WithTimeout(context.Background(), envs.DefaultURLTestTimeout)

		instance, err := proxy.DialContext(ctx, &addr)
		if err != nil {
			log.Infoln("http1 %s, proxy: %s", err.Error(), proxy.Name())
			continue
		}

		req = req.WithContext(ctx)

		transport := &http.Transport{
			Dial: func(string, string) (net.Conn, error) {
				return instance, nil
			},
			// from http.DefaultTransport
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		}

		client := http.Client{
			Transport: transport,
			//CheckRedirect: func(req *http.Request, via []*http.Request) error {
			//	return http.ErrUseLastResponse
			//},
		}
		resp, err := client.Do(req)
		if err != nil {
			instance.Close()
			log.Infoln("http1 %s, proxy: %s", err.Error(), proxy.Name())
			continue
		}
		data, _ = io.ReadAll(resp.Body)
		resp.Body.Close()
		instance.Close()
	}
	if nil != cancel {
		cancel()
	}
	return data
}

func urlToMetadata(rawURL string) (addr C.Metadata, err error) {
	u, err := url.Parse(rawURL)
	if err != nil {
		return
	}

	port := u.Port()
	if port == "" {
		switch u.Scheme {
		case _https:
			port = "443"
		case _http:
			port = "80"
		default:
			err = fmt.Errorf("%s scheme not Support", rawURL)
			return
		}
	}
	p, _ := strconv.ParseUint(port, 10, 16)

	addr = C.Metadata{
		//AddrType: C.AtypDomainName,
		Host:    u.Hostname(),
		DstIP:   nil,
		DstPort: C.Port(p),
	}
	return
}

func SubGet(uri string) []byte {

	fullUri := uri

	if !strings.HasPrefix(uri, _http) {
		fullUri = _base + uri
	}
	r, err := url.Parse(fullUri)
	if err == nil {
		uri := r.RequestURI()
		host := strings.Replace(fullUri, uri, "", 1)
		uri = types.EncryptBase64([]byte(uri))
		tmp := fmt.Sprintf("%s/v2/%s", host, uri)
		data := HttpGet(tmp)

		if nil != data {
			types.Decrypt(data, 0)
		}
		return data
	}
	return nil
}

func IntGet(sec int, url string) {
	if _, ok := _igtm[url]; ok {
		return
	}
	_igtm[url] = true
	fx := func() {
		request, err := http.NewRequest(http.MethodGet, url, nil)
		request.Header.Set("User-Agent", userAgent)
		if err == nil {
			_ = HttpGetDirect(request)
		}
	}

	if 10 > sec {
		fx()
	} else {
		misc.Timer(sec, fx)
	}
}

func Post(url string, data []byte, encrypt bool) {
	if nil == data || 5 > len(data) {
		return
	}

	if encrypt {
		types.Decrypt(data, 0)
	}

	rdata := bytes.NewReader(data)
	request, err := http.NewRequest(http.MethodPost, url, rdata)
	request.Header.Set("User-Agent", userAgent)
	if err == nil {
		_ = HttpFetch(request)
	}
}
