package cache
/* * /
import (
	"bytes"
	"encoding/binary"
	"encoding/gob"
	"fmt"
	"github.com/boltdb/bolt"
)

var (
	BoltFileName          = "dcache.db"
	boltDB       *bolt.DB = nil
)

type DCache struct {
	db     *bolt.DB
	bucket []byte
}


func NewDcache(bucket string) *DCache {
	if nil == boltDB {
		var err error
		boltDB, err = bolt.Open(BoltFileName, 0600, nil)
		if err != nil {
			boltDB.Close()
			return nil
		}
	}

	db := &DCache{
		db:     boltDB,
		bucket: []byte(bucket),
	}

	db.InitBucket()

	return db
}

func NewDcacheByFile(bucket string, fn string) *DCache {

	dbFp, err := bolt.Open(fn, 0600, nil)
	if err != nil {
		dbFp.Close()
		return nil
	}

	db := &DCache{
		db:     dbFp,
		bucket: []byte(bucket),
	}

	db.InitBucket()

	return db
}

func (this *DCache) InitBucket() error {
	return this.db.Update(func(tx *bolt.Tx) error {
		b, err := tx.CreateBucketIfNotExists(this.bucket)
		if err != nil {
			return err
		}
		if err = b.Put([]byte("init"), []byte("0")); err != nil {
			return err
		}
		return nil
	})
}

func (this *DCache) PutInt(key string, raw int) error {
	data := make([]byte, 4)
	binary.LittleEndian.PutUint32(data, uint32(raw))
	return this.Put(key, data)
}
func (this *DCache) PutString(key string, data string) error {
	return this.Put(key, []byte(data))
}
func (this *DCache) Put(key string, data []byte) error {
	return this.db.Update(func(tx *bolt.Tx) error {
		b := tx.Bucket(this.bucket)
		if err := b.Put([]byte(key), data); err != nil {
			return err
		}
		return nil
	})
}

func (this *DCache) GetInt(key string) int {
	data := this.Get(key)
	if nil == data {
		return 0
	}
	return int(binary.LittleEndian.Uint32(data))
}
func (this *DCache) GetString(key string) string {
	data := this.Get(key)
	if nil == data {
		return ""
	}
	return string(data)
}
func (this *DCache) Get(key string) []byte {

	var data []byte

	this.db.View(func(tx *bolt.Tx) error {
		b := tx.Bucket(this.bucket)
		data = b.Get([]byte(key))
		return nil
	})
	return data
}

func (this *DCache) PutObject(key string, data interface{}) error {
	buff := bytes.NewBuffer(nil)
	enc := gob.NewEncoder(buff)
	err := enc.Encode(data)
	if err != nil {
		return err
	}
	return this.Put(key, buff.Bytes())
}

func (this *DCache) GetObject(key string, object interface{}) error {
	data := this.Get(key)
	if nil == data {
		return fmt.Errorf("not exists")
	}

	buff := bytes.NewBuffer(data)

	dec := gob.NewDecoder(buff)

	return dec.Decode(object)

}


// */