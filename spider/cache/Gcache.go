package cache

import (
	"encoding/gob"
	"os"
	"sync"
	"time"
)

type GoCache struct {
	fn        string
	data      map[string]interface{}
	vtype     interface{}
	locker    *sync.Mutex
	hasModify bool
}

func NewGoCache(fn string) *GoCache {
	return &GoCache{fn: fn, locker: &sync.Mutex{}}
}
func NewDataCache(fn string) *GoCache {
	dc := GoCache{fn: fn, vtype: map[string]interface{}{}, locker: &sync.Mutex{}}
	dc.Load(&dc.data)
	if nil == dc.data {
		dc.data = make(map[string]interface{})
	}

	return &dc
}

func (this *GoCache) SetType(t interface{}) {
	this.vtype = t
}

func (this *GoCache) PutInt(k string, v int) {
	this.hasModify = true
	this.locker.Lock()
	this.data[k] = v
	this.locker.Unlock()
}

func (this *GoCache) GetInt(k string) int {
	this.locker.Lock()
	v := this.data[k]
	this.locker.Unlock()
	if nil == v {
		return 0
	}
	return v.(int)
}

func (this *GoCache) PutExp(k string, v int) {
	this.hasModify = true
	this.locker.Lock()
	this.data[k] = int64(v) + time.Now().Unix()
	this.locker.Unlock()
}
func (this *GoCache) IsNotExp(k string) bool {
	this.locker.Lock()
	v := this.data[k]
	this.locker.Unlock()
	if nil == v {
		return false
	}
	return v.(int64) > time.Now().Unix()

}

func (this *GoCache) PutString(k string, v string) {
	this.hasModify = true
	this.locker.Lock()
	this.data[k] = v
	this.locker.Unlock()
}

func (this *GoCache) GetString(k string) string {
	this.locker.Lock()
	v := this.data[k]
	this.locker.Unlock()
	if nil == v {
		return ""
	}
	return v.(string)
}

func (this *GoCache) Save(items interface{}) {
	if nil == this || nil == items {
		return
	}
	this.locker.Lock()
	defer this.locker.Unlock()

	if nil != this.vtype {
		gob.Register(this.vtype)
	}
	fp, err := os.Create(this.fn)
	if err != nil {
		//fmt.Println(this.fn, err)
		return
	}
	defer fp.Close()
	encoder := gob.NewEncoder(fp)
	err = encoder.Encode(items)
	if err != nil {
		//fmt.Println(this.fn, err)
		return
	}

	this.hasModify = false
}

func (this *GoCache) Load(items interface{}) {
	if nil != this.vtype {
		gob.Register(this.vtype)
	}
	fp, err := os.Open(this.fn)
	if err != nil {
		//fmt.Println(this.fn, err)
		return
	}
	decoder := gob.NewDecoder(fp)
	err = decoder.Decode(items)
	if err != nil {
		//fmt.Println(this.fn, err)
		return
	}
	fp.Close()

}

func (this *GoCache) AutoClean() {
	this.data = nil
	this.data = make(map[string]interface{})
}
func (this *GoCache) AutoSave() {
	if nil == this.data || 1 > len(this.data) || !this.hasModify {
		return
	}
	this.Save(this.data)
}
