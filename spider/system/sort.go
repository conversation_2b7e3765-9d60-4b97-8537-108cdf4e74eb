package system

//import (
//	C "clash-foss/constant"
//)
/*

type PList []C.Proxy

func (s PList) Len() int      { return len(s) }
func (s PList) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s PList) Less(i, j int) bool {
	a := s[i].LastDelay()
	b := s[j].LastDelay()
	if 1 > a {
		a = 65535
	}
	if 1 > b {
		b = 65535
	}

	return a < b
}

func LoadOnlyProxies() *[]C.Proxy {
	pid := 0
	proxies := tunnel.Proxies()
	items := make([]C.Proxy, len(proxies))
	for n, m := range proxies {
		if !IsProxyOnly(m) {
			continue
		}

		items[pid] = proxies[n]
		pid += 1
	}
	result := items[0:pid]
	return &result
}

func LoadSortedProxies(total int) []C.Proxy {
	onlyProxies := LoadOnlyProxies()
	proxies := *onlyProxies

	if nil == onlyProxies || 1 > len(proxies) {
		return nil
	}
	sort.Sort(PList(proxies))
	size := len(proxies)
	if size > total {
		size = total
	}
	return proxies[0:size]
}

func collectProviders(providers []provider.ProxyProvider) []C.Proxy {
	result := make([]C.Proxy, 0, 128)

	for _, p := range providers {
		for _, px := range p.Proxies() {
			//name := px.Name()
			//title := name
			//subtitle := px.Type().String()

			result = append(result, px)
		}
	}

	return result
}
// */
