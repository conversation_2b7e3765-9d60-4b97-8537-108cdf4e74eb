package system

import (
	"clash-foss/adapter"
	"clash-foss/adapter/outboundgroup"
	C "clash-foss/constant"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/tunnel"
	"clash-foss/tunnel/statistic"
	"fmt"
	"math/rand"
	"net"
)

var (
	skipLists        = make(map[string]bool)
	aliveMax  uint16 = 0xffff
)

func init() {
	data.CountryResloveFunc = ResloveCountry
}

func ResloveCountry(ip string) (name string) {
	name = ""
	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()

	hosts, _ := net.LookupHost(ip)

	name = envs.ResloveNameFunc(hosts[0])
	return
}

func GetRandomProxy() C.Proxy {
	if 1 > envs.Total {
		return nil
	}
	i := 0
	items := tunnel.Proxies()
	size := len(items)
	if 2 > size {
		return nil
	}
	j := rand.Intn(size - 1)
	for k, p := range items {
		if !p.Alive() {
			continue
		}
		if j == i {
			return items[k]
		}
		i += 1
	}
	return nil
}

func GetAllProxies() []C.Proxy {
	items := tunnel.Proxies()
	size := len(items)
	if 1 > size {
		return nil
	}
	index := 0
	results := make([]C.Proxy, size)
	for _, p := range items {
		w, ok := p.(*adapter.Proxy)
		if !ok {
			continue
		}
		results[index] = w
		index += 1
	}

	return results[0:index]
}
func GetAliveProxy() interface{} {
	items := tunnel.LoadSortedProxies(3)
	size := len(items)
	if 1 > size {
		return nil
	}
	//for k, p := range items {
	//	delay := p.LastDelay()
	//	if !p.Alive() || 1 > delay || aliveMax == delay {
	//		continue
	//	}
	//	return items[k]
	//}
	return items[0]
}

func GetDeadProxies() *map[string]bool {
	items := tunnel.Proxies()
	size := len(items)
	if 1 > size {
		return nil
	}

	names := make(map[string]bool)
	for _, p := range items {
		if p.Alive() {
			continue
		}
		t := p.LastDelay()
		if t > 10 && 65530 > t {
			continue
		}

		names[p.Addr()] = true
	}

	return &names
}

//func SetGlobalProxy(name string) error {
//	proxies := tunnel.Proxies()
//	if proxy, exist := proxies["GLOBAL"].(*adapter.Proxy); exist {
//		if selector, ok := proxy.ProxyAdapter.(*outboundgroup.Selector); ok {
//			return selector.Set(name)
//		} else {
//			return fmt.Errorf("Not exits Selector")
//		}
//	} else {
//		return fmt.Errorf("Not exits GLOBAL")
//	}
//	return fmt.Errorf("Not exits")
//}

func AutoChooseFastSigleProxy() string {
	proxies := tunnel.Proxies()
	var selector *outboundgroup.Selector
	total := len(proxies)

	if 2 > total {
		return ""
	}

	if total > 5 {
		total = 5
	}

	//只在排名前N的代理中切換
	if len(skipLists) >= total {
		skipLists = make(map[string]bool)
	}

	if proxy, exist := proxies["GLOBAL"].(*adapter.Proxy); exist {
		skipLists[proxy.Name()] = true
		if s, ok := proxy.ProxyAdapter.(*outboundgroup.Selector); ok {
			selector = s
		}
	}

	//排序
	//var allProxies []C.Proxy
	//tmp := tunnel.LoadOnlyProxies()
	//allProxies = *tmp
	//if nil == tmp || 1 > len(allProxies) {
	//	return ""
	//}
	//
	//sort.Sort(PList(allProxies))

	allProxies := tunnel.LoadSortedProxies(30)

	size := len(allProxies)
	if size > 30 {
		size = 30
	}

	for i := 0; size > i; i++ {
		m := allProxies[i]
		if _, ok := skipLists[m.Name()]; !ok {
			_ = selector.Set(m.Name())
			skipLists[m.Name()] = true
			return m.Name()
		}
	}
	return ""
}

func GetFastProxies() interface{} {
	//排序
	//var allProxies []C.Proxy
	//tmp := LoadOnlyProxies()
	//allProxies = *tmp
	//if nil == tmp || 1 > len(allProxies) {
	//	return ""
	//}
	//
	//sort.Sort(PList(allProxies))

	allProxies := tunnel.LoadSortedProxies(30)
	size := len(allProxies)
	if size > 30 {
		size = 30
	}

	//for i := 0; size > i; i++ {
	//	m := allProxies[i]
	//	if _, ok := skipLists[m.Name()]; !ok {
	//		_ = selector.Set(m.Name())
	//		skipLists[m.Name()] = true
	//		return m.Name()
	//	}
	//}
	return allProxies[0]
}

func CloseAllConnections() {
	snapshot := statistic.DefaultManager.Snapshot()
	for _, c := range snapshot.Connections {
		_ = c.Close()
	}
}

func GetGroupNameAutoFast() C.Proxy {
	proxies := tunnel.Proxies()
	for _, m := range proxies {
		w, ok := m.(*adapter.Proxy)
		if !ok {
			continue
		}
		t := m.Type()
		if t == C.AutoFast {
			return w
		}
	}
	return nil
}

func QueryProxyGroupNames() string {
	mode := tunnel.Mode()

	if mode == tunnel.Global {
		return "GLOBAL"
	}

	if mode != tunnel.Rule {
		return mode.String()
	}

	global := tunnel.Proxies()["GLOBAL"].(*adapter.Proxy).ProxyAdapter.(outboundgroup.ProxyGroup)
	proxies := global.Providers()[0].Proxies()

	for _, p := range proxies {
		if _, ok := p.(*adapter.Proxy).ProxyAdapter.(outboundgroup.ProxyGroup); ok {
			if p.Type() == C.Selector {
				return p.Name()
			}
		}
	}

	return mode.String()
}

func SetAtAll(fastName string) {
	proxies := tunnel.Proxies()

	if 1 > len(fastName) {
		for _, m := range proxies {
			if w, ok := m.(*adapter.Proxy); ok {
				if m.Type() == C.AutoFast {
					fastName = w.Name()
					break
				}
			}
		}
	}

	if 1 > len(fastName) {
		return
	}

	for k, p := range proxies {
		if g, ok := p.(*adapter.Proxy).ProxyAdapter.(outboundgroup.ProxyGroup); ok {
			if p.Type() == C.Selector {
				isok := false
				var err error
				if selector, ok := g.(*outboundgroup.Selector); ok {
					t := selector.Unwrap(nil).Type()
					if t == C.Selector || "GLOBAL" == k {
						isok = true
						err = selector.Set(fastName)
					}
					fmt.Println("====SetAtAll======>", k, fastName, t, isok, err)
				}
			}
		}
	}
}
