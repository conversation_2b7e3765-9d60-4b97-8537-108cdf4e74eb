package misc

import (
	"sync"
)

func SafeGo(fx func()) {
	defer func() {
		if err := recover(); err != nil {
			//emsg := ""
			//switch err.(type) {
			//case string:
			//	emsg = err.(string)
			//case error:
			//	emsg = err.(error).Error()
			//default:
			//	emsg = "unknow error"
			//}
			//
			//stack := string(debug.Stack())

			//utils.AppWarning("panic", err, stack)
			//mailer.SendMail("panic", emsg, stack)
		}
	}()

	fx()
}



// Safe wraps any value to a mutex.
type Safe struct {
	m     sync.RWMutex
	value interface{}

	//stringList *[]string
}

// New create a new Safe instance.
func NewSafeValue(value interface{}) *Safe {
	return &Safe{
		m:     sync.RWMutex{},
		value: value,
	}
}
//func NewSafeStringList( vals *[]string) *Safe {
//	return &Safe{
//		m:     sync.RWMutex{},
//		stringList: vals,
//	}
//}

// Get returns the value in a thread-safe fashion.
func (s *Safe) Get() interface{} {
	s.m.RLock()
	defer s.m.RUnlock()
	return s.value
}

//func (s *Safe) GetStringList() *[]string {
//	s.m.RLock()
//	defer s.m.RUnlock()
//	return s.stringList
//}

// Set sets a new value in a thread-save fashion.
func (s *Safe) Set(value interface{}) {
	s.m.Lock()
	defer s.m.Unlock()
	s.value = value
}