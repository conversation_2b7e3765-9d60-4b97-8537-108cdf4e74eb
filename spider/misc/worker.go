package misc


type Worker[T any] struct {
	size int
	jobs chan T
	fn   func(T)
}

func NewWorker[T any](size int) *Worker[T] {
	return &Worker[T]{size: size}
}

func (this *Worker[T]) Send(t T) {
	this.jobs <- t
}

func (this *Worker[T]) thread() {
	for {
		job := <-this.jobs
		this.fn(job)
	}
}

func (this *Worker[T]) Start(fnc func(T)) {
	this.fn = fnc
	this.jobs = make(chan T)
	//log.Info("Worker start at size: %d", this.size)
	for i := 0; i < this.size; i++ {
		go this.thread()
	}
}
