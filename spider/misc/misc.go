package misc

import (
	"clash-foss/spider/envs"
	"fmt"
	"net"
	"time"
)

var (
	timer1tasks = make([]func(), 0)
)

func Ping(host ...string) bool {
	var server string
	switch len(host) {
	case 1:
		server = host[0]
		break

	case 2:
		server = fmt.Sprintf("%s:%s", host[0], host[1])
		break
	default:
		return false
	}

	//conn, err := net.Dial("tcp", server)
	conn, err := net.DialTimeout("tcp", server, envs.DefaultConnectTimeout)
	if err != nil {
		return false
	}

	conn.Close()
	return true
}

func Timer(sec int, fn func()) {
	c := time.NewTicker(time.Duration(sec) * time.Second)
	go func() {
		for {
			<-c.C
			fn()
		}
	}()

}

func Add1MinuteTask(fn func()) {
	timer1tasks = append(timer1tasks, fn)
	if 1 == len(timer1tasks) {
		Timer(60, func() {
			for _, fx := range timer1tasks {
				fx()
			}
		})
	}
}
