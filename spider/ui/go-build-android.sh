#!/bin/sh

set -ex

export ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.0.7599858
export TOOLCHAINS=/Users/<USER>/source/Android/arm64-android-toolchain-target24
export CC=${TOOLCHAINS}/bin/aarch64-linux-android-clang
export CXX=${TOOLCHAINS}/bin/aarch64-linux-android-clang++
export GOMOBILE=$HOME/go/pkg/gomobile
export GOOS="android"
export GOARCH="arm64"
export CGO_CFLAGS="-target aarch64-none-linux-android --sysroot ${TOOLCHAINS}/sysroot"
export CGO_CPPFLAGS="-target aarch64-none-linux-android --sysroot ${TOOLCHAINS}/sysroot"
export CGO_LDFLAGS="-target aarch64-none-linux-android --sysroot ${TOOLCHAINS}/sysroot"
# export GOARM=7
export CGO_ENABLED=1
go build -a -pkgdir="$GOMOBILE/pkg_android_arm64" -ldflags '-extldflags "-pie -fPIE -fPIC"' "$