package main

import (
	C "clash-foss/constant"
	"clash-foss/spider/envs"
	"clash-foss/spider/system"
	"clash-foss/tunnel"
	"fmt"
	"github.com/gdamore/tcell/v2"
	log "github.com/sirupsen/logrus"
	"os"
	"strconv"
	"strings"
)

var (
	currentSelection = 0
	logwriter        *LogWriter
)

type LogWriter struct {
	screen      tcell.Screen
	logRows     []string // 存储日志行
	logRowCount int      // 日志行数
	maxLogRows  int      // 最大日志行数
}

func NewLogWriter(screen tcell.Screen, maxLogRows int) *LogWriter {
	return &LogWriter{
		screen:     screen,
		logRows:    make([]string, 0, maxLogRows),
		maxLogRows: maxLogRows,
	}
}

func (lw *LogWriter) Write(p []byte) (n int, err error) {
	lw.logRows = append(lw.logRows, string(p))
	if len(lw.logRows) > lw.maxLogRows {
		lw.logRows = lw.logRows[len(lw.logRows)-lw.maxLogRows:]
	}

	lw.RefreshLogArea()
	return len(p), nil
}

func (lw *LogWriter) RefreshLogArea() {
	width, height := lw.screen.Size()
	style := tcell.StyleDefault

	// 清除日志区域
	for y := height - lw.maxLogRows; y < height; y++ {
		for x := 0; x < width; x++ {
			lw.screen.SetContent(x, y, ' ', nil, style)
		}
	}

	// 显示最新的日志信息
	start := len(lw.logRows) - lw.maxLogRows
	if start < 0 {
		start = 0
	}
	for i, logLine := range lw.logRows[start:] {
		line := strings.ReplaceAll(logLine, "\n", "")
		for x, c := range line[:min(len(line), width)] {
			lw.screen.SetContent(x, height-lw.maxLogRows+i, c, nil, style)
		}
	}

	lw.screen.Show()
}

func clearScreen() {
	// ANSI 转义序列：\033[2J 清屏，\033[H 移动光标到左上角
	fmt.Print("\033[2J\033[H")
}

func optionV2() {
	// Initialize tcell screen
	screen, err := tcell.NewScreen()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating screen: %v\n", err)
		os.Exit(1)
	}
	if err := screen.Init(); err != nil {
		fmt.Fprintf(os.Stderr, "Error initializing screen: %v\n", err)
		os.Exit(1)
	}
	defer screen.Fini()

	logwriter = NewLogWriter(screen, 12)
	log.SetOutput(logwriter)

	for {
		allProxies = tunnel.LoadSortedProxies(50)
		showSelectionUI(screen, allProxies, func(p C.Proxy) {
			if p == nil {
				return
			}
			choose := p.Name()
			system.SetAtAll(choose)
			knockingOkAdapter = nil
			system.CloseAllConnections()
			log.Println("=====>choose<=======", choose)
		})
	}
}

func showSelectionUI(screen tcell.Screen, servers []C.Proxy, executeCommand func(C.Proxy)) {
	if 1 > len(servers) {
		log.Println("No SSH servers found")
		os.Exit(0)
	}

	clearScreen()
	// Reset current selection at start
	delayFilter := true
	currentSelection = 0
	numInput := ""

	// Set screen style
	defStyle := tcell.StyleDefault.Background(tcell.ColorReset).Foreground(tcell.ColorReset)
	selectedStyle := tcell.StyleDefault.Background(tcell.ColorReset).Foreground(tcell.ColorGreen).Bold(true)
	infoStyle := tcell.StyleDefault.Background(tcell.ColorReset).Foreground(tcell.ColorYellow)

	// Event loop
	for {
		// Clear screen
		screen.Clear()
		logwriter.RefreshLogArea()

		choose := getGlobalProxyGroup().Now()

		// Display title and instructions
		drawText(screen, 0, 0, defStyle, "Available Servers:")
		drawText(screen, 0, 1, defStyle, "-------------------------------------------------------------------")

		index := 0
		for i, cmd := range servers {
			markDelay := ""
			delay := int(cmd.LastDelay())
			if delay > 65530 {
				delay = 0
			}
			if 1 > delay {
				markDelay = ""
				if delayFilter {
					continue
				}
			} else {
				markDelay = fmt.Sprintf("%d", delay)
			}

			index += 1
			selMarker := " "

			if i == currentSelection {
				selMarker = ">"
				drawText(screen, 0, i+2, selectedStyle, fmt.Sprintf("%s%3d. %s %s", selMarker, i+1, cmd.Name(), markDelay))
			} else if choose == cmd.Name() {
				drawText(screen, 0, i+2, infoStyle, fmt.Sprintf("%s%3d. %s %s", selMarker, i+1, cmd.Name(), markDelay))
			} else {
				drawText(screen, 0, i+2, defStyle, fmt.Sprintf("%s%3d. %s %s", selMarker, i+1, cmd.Name(), markDelay))
			}

			if index >= 20 {
				break
			}
		}

		y := index + 2

		status := fmt.Sprintf("Use ↑/↓ to navigate, Current: [%d] => %s", envs.Total, choose)
		drawText(screen, 0, y+1, defStyle, status)
		drawText(screen, 0, y+2, defStyle, "c - Health Check [c http..]")
		drawText(screen, 0, y+3, defStyle, "m - Switch Global/Rule")
		drawText(screen, 0, y+4, defStyle, "a - Switch to AutoFast")
		drawText(screen, 0, y+5, defStyle, "x - Switch Loadbalance")
		drawText(screen, 0, y+6, defStyle, "f - Switch delay filter")
		drawText(screen, 0, y+7, defStyle, "v - Switch debug mode")
		drawText(screen, 0, y+8, defStyle, "r - Switch Fast mode")
		drawText(screen, 0, y+9, defStyle, "w - Start watch once")
		drawText(screen, 0, y+10, defStyle, "t - test and knocking [t xxx.x..]")
		drawText(screen, 0, y+11, defStyle, "z - restart / s - Status / e - Exit")

		// Update screen
		screen.Show()

		// Handle events
		ev := screen.PollEvent()
		switch ev := ev.(type) {
		case *tcell.EventKey:
			switch ev.Key() {
			case tcell.KeyEscape, tcell.KeyCtrlC:
				return
			case tcell.KeyEnter:
				if len(numInput) > 0 {
					if num, err := strconv.Atoi(numInput); err == nil {
						if num > 0 && num <= len(servers) {
							//screen.Fini()
							executeCommand(servers[num-1])
							return
						}
					}
					numInput = ""
				} else {
					// Use current selection
					if currentSelection >= 0 && currentSelection < len(servers) {
						//screen.Fini()
						executeCommand(servers[currentSelection])
						return
					}
				}
			case tcell.KeyUp:
				// Arrow keys take priority over number input
				numInput = "" // Clear number input when using arrow keys
				currentSelection--
				if currentSelection < 0 {
					currentSelection = 0
				}
			case tcell.KeyDown:
				// Arrow keys take priority over number input
				numInput = "" // Clear number input when using arrow keys
				currentSelection++
				if currentSelection >= len(servers) {
					currentSelection = len(servers) - 1
				}
			case tcell.KeyRune:
				r := ev.Rune()

				if 'q' == r || 'z' == r || ' ' == r || '\'' == r {
					return
				}

				if r >= '0' && r <= '9' {
					numInput += string(r)
					// If the number is valid, highlight that option
					if num, err := strconv.Atoi(numInput); err == nil {
						if num > 0 && num <= len(servers) {
							currentSelection = num - 1
							executeCommand(servers[num-1])
						}
					}
					continue
				}
				t := checkUcmd(string(r))
				switch t {
				case 1:
					delayFilter = !delayFilter
					continue
				case 2:
					//exit
					return
				case 3:
					//continue
					continue
				case 4:
					//proxyFilter = userCommand
					continue
				}
			case tcell.KeyBackspace, tcell.KeyBackspace2:
				if len(numInput) > 0 {
					numInput = numInput[:len(numInput)-1]
				}
			}
		case *tcell.EventResize:
			screen.Sync()
		}
	}
}

// Helper function to draw text on the screen
func drawText(screen tcell.Screen, x, y int, style tcell.Style, text string) {
	for i, r := range []rune(text) {
		screen.SetContent(x+i, y, r, nil, style)
	}
}
