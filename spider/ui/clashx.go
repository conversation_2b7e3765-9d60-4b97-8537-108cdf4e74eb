package main

import (
	"bufio"
	"clash-foss/adapter"
	"clash-foss/adapter/outboundgroup"
	"clash-foss/config"
	C "clash-foss/constant"
	"clash-foss/hub"
	"clash-foss/hub/executor"
	"clash-foss/log"
	"clash-foss/spider"
	"clash-foss/spider/envs"
	"clash-foss/spider/fullSys"
	"clash-foss/spider/hook"
	"clash-foss/spider/manager"
	"clash-foss/spider/system"
	"clash-foss/tunnel"
	"clash-foss/tunnel/statistic"
	"context"
	"flag"
	"fmt"
	"github.com/gookit/color"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"syscall"
	"time"
)

var (
	buildver         = "2025-6-23"
	version          bool
	homedir          string
	configFile       string
	runAsProxyServ   bool
	autoCheckTime    int
	autoCheckAtStart bool
	isDebug          bool
	apiURL           = ""
	proxyURI         = ""
	proxyName        = ""
	passwd           = []int{65431, 7712, 8823, 9934}
	colorRed         = color.FgRed.Render
	colorGreen       = color.FgGreen.Render
	testUrl          = "https://www.youtube.com/#"
	allProxies       []C.Proxy
	showProxies      []C.Proxy
	testTimeout      = time.Duration(3 * time.Second)
	proxyType        = 2
	knockRunning     = false

	knockingOkAdapter C.ProxyAdapter

	hostTable = []string{
		"************* m1",
		"************** y01",
		"************* tb1",
		"************** tb2",
	}
)

type GConfig struct {
	All     []string      `json:"all"`
	History []interface{} `json:"history"`
	Now     string        `json:"now"`
	Type    string        `json:"type"`
}

type GDelay struct {
	Delay int `json:"delay"`
}

func init() {
	flag.StringVar(&homedir, "d", "", "set configuration directory")
	flag.StringVar(&configFile, "f", "", "configuration file")
	flag.BoolVar(&version, "v", false, "show current version of clash")
	flag.BoolVar(&runAsProxyServ, "as", false, "run as proxy")
	flag.BoolVar(&autoCheckAtStart, "c", true, "auto check at start")
	flag.IntVar(&autoCheckTime, "t", 30, "run as proxy auto check time")
	flag.IntVar(&proxyType, "pt", 1, "proxy type 01clash, 10web, 4不启用扫描")
	flag.BoolVar(&isDebug, "dg", false, "is debug mode")
	flag.BoolVar(&envs.FastPing, "fp", true, "fast ping")
	flag.Parse()
}

func printVersion() {
	fmt.Printf("Clash [%s] %s %s %s\n", buildver, runtime.GOOS, runtime.GOARCH, C.Path.HomeDir())
}

func knocking(ips string) {
	if 1 > len(ips) {
		return
	}
	elms := strings.FieldsFunc(ips, func(r rune) bool {
		return r == ' ' || r == ','
	})

	for _, elm := range elms {
		ip := resloveIp(elm)
		if 1 > len(ip) {
			continue
		}
		knockRunning = true
		doKnocking2(ip)
	}
	knockRunning = false
}
func doKnocking2(ip string) {
	if 1 > len(ip) {
		return
	}

	metadata := C.Metadata{
		//AddrType: C.AtypIPv4,
		DstIP:   net.ParseIP(ip),
		DstPort: 0,
	}

	//使用最后一次成功的代理
	if nil != knockingOkAdapter {
		tt := 3
		for j := 0; j < tt; j++ {
			err := KnockingWithDialer(metadata, knockingOkAdapter, j == 0)
			if nil == err {
				fmt.Println("Knock", colorGreen(ip), colorRed("success"))
				return
			}
		}
		knockingOkAdapter = nil
	}

	fmt.Println("Knocking", colorGreen(ip))

	proxies := tunnel.LoadSortedProxies(10)
	for j := 0; j < 2; j++ {
		for _, elm := range proxies {
			adp := elm.(*adapter.Proxy).ProxyAdapter
			err := KnockingWithDialer(metadata, adp, j == 0)
			if nil == err {
				knockingOkAdapter = adp
				name := adp.Name()
				system.SetAtAll(name)
				fmt.Println("Knock", colorGreen(ip), colorRed("success"))
				return
			}
			if !knockRunning {
				return
			}
		}
	}
}

func KnockingWithDialer(metadata C.Metadata, adp C.ProxyAdapter, testOnly bool) error {
	var n int
	var err error
	var c net.Conn

	wb := []byte("1")
	TAG := adp.Name() + " ===> "

	sshTest := func() error {
		rb := make([]byte, 10)
		metadata.DstPort = 22
		ctx, cancel := context.WithTimeout(context.Background(), C.DefaultTCPTimeout)
		defer cancel()

		c, err = adp.DialContext(ctx, &metadata)
		if err != nil {
			fmt.Println("Knoking 101/", TAG, err.Error())
			return err
		}
		_ = c.SetReadDeadline(time.Now().Add(3 * time.Second))
		n, err = c.Read(rb)
		if 3 > n || nil != err {
			fmt.Println("Knoking 102/", TAG, err.Error())
			return err
		}
		sshflag := string(rb)
		if !strings.Contains(sshflag, "SSH") {
			fmt.Println("Knoking 103/", TAG, sshflag)
			return fmt.Errorf("===> sshflag error: %s", sshflag)
		}

		fmt.Println(TAG, colorGreen(metadata.DstIP.String()), "Test OK")
		return nil
	}

	err = sshTest()
	if nil == err || testOnly {
		return err
	}

	fmt.Println(TAG, "Knocking", colorGreen(metadata.DstIP.String()))

	for _, port := range passwd {
		j := 3
		metadata.DstPort = C.Port(port)
		for i := 0; i <= j; i++ {
			ctx, cancel := context.WithTimeout(context.Background(), C.DefaultTCPTimeout)
			c, err = adp.DialContext(ctx, &metadata)
			if err != nil {
				cancel()
				fmt.Println("Knoking 104/", TAG, port, err.Error())
				continue
			}
			n, err = c.Write(wb)
			_ = c.Close()
			cancel()
			break
		}
	}

	return sshTest()
}

func resloveIp(mURL string) string {
	host := mURL
	if strings.Contains(mURL, "//") {
		u, err := url.Parse(mURL)
		if err != nil {
			fmt.Println("Could not get IPs: %v\n", err)
			return ""
		}

		host, _, _ = net.SplitHostPort(u.Host)
	}

	for _, elm := range hostTable {
		if strings.Contains(elm, host) {
			m := strings.Split(elm, " ")
			return m[0]
		}
	}

	lookup, err := net.LookupIP(host)
	if err != nil {
		fmt.Println("Could not get IPs: %v\n", err)
		return ""
	}

	return lookup[0].String()
}

func LoadSortedProxies() {
	allProxies = tunnel.LoadSortedProxies(50)
}

func findBestProxy() {
	LoadSortedProxies()
	hook.HealthCheckAll(allProxies, testUrl)
	changeProxy(allProxies[0].Name())
}

func findProxy(name string, match bool) C.Proxy {
	for _, adp := range allProxies {
		t := adp.Name()
		if t == name {
			return adp
		}
		if match && strings.Contains(t, name) {
			return adp
		}
	}
	return nil
}

func getGlobalProxyGroup() outboundgroup.ProxyGroup {
	proxies := tunnel.Proxies()
	choose := proxies["GLOBAL"].(*adapter.Proxy).ProxyAdapter.(outboundgroup.ProxyGroup)
	return choose
}

func manualUseFast() {
	allProxies = tunnel.LoadSortedProxies(50)
	showProxies = make([]C.Proxy, 0)
	name := allProxies[0].Name()
	if len(name) > 0 {
		system.SetAtAll(name)
		knockingOkAdapter = nil
		system.CloseAllConnections()
	}
}

func options() {
	var count = 0
	var mark string
	var markDelay string
	var userCommand string
	var proxyFilter = ""
	delayFilter := true
	autoCheckAtStart = false
	screenReader := bufio.NewReader(os.Stdin)

	for {
		//screen.Clear()
		//screen.MoveTopLeft()

		fmt.Println("===========================")
		fmt.Printf("==========%s============\n", C.Path.Config())
		fmt.Println("===========================")

		time.Sleep(1 * time.Second)

		allProxies = tunnel.LoadSortedProxies(50)

		if autoCheckAtStart {
			autoCheckAtStart = false
			system.AutoChooseFastSigleProxy()
		}

		choose := getGlobalProxyGroup().Now()
		mode := tunnel.Mode().String()

		showCount := 0
		hasFilter := len(proxyFilter) > 0
		totalNum := len(allProxies)
		showProxies = make([]C.Proxy, 0, totalNum)
		showLines := make([][]any, 0, totalNum)

		for pid := totalNum - 1; pid >= 0; pid-- {
			mark = ""
			markDelay = ""
			elm := allProxies[pid]
			name := elm.Name()

			if hasFilter && !(name == proxyFilter || strings.Contains(name, proxyFilter)) {
				continue
			}

			delay := int(elm.LastDelay())
			if delay > 65530 {
				delay = 0
			}
			if 1 > delay {
				markDelay = ""
				if delayFilter {
					continue
				}
			} else {
				markDelay = colorGreen(delay)
			}

			if name == choose {
				mark = colorGreen("*")
			}

			showCount += 1
			score := elm.Score()
			showProxies = append(showProxies, elm)
			t := []any{"", name, markDelay, mark, colorGreen(score)}
			showLines = append(showLines, t)
		}

		total := len(showLines)
		for i := range showLines {
			total -= 1
			lines := showLines[i]
			lines[0] = colorRed(total)
			fmt.Println(lines...)
		}

		//快切代理
		//fasts := outboundgroup.GetFasts()
		//if len(fasts) > 0 {
		//	fmt.Println("=============Fast==============")
		//	for i := len(fasts) - 1; i >= 0; i-- {
		//		elm := fasts[i]
		//		score := elm.Score()
		//		fmt.Println(colorRed(i), elm.Name(), colorGreen(elm.LastDelay()), colorGreen(score))
		//	}
		//}

		proxyFilter = ""
		fmt.Printf("Total: %d/%d/[%d], Current: %s - %s\n", totalNum, showCount, envs.Total, mode, choose)
		fmt.Println("===========================")
		fmt.Println(colorRed("c"), "Health Check [c http..]")
		fmt.Println(colorRed("m"), "Switch Global/Rule")
		fmt.Println(colorRed("a"), "Switch to AutoFast")
		fmt.Println(colorRed("x"), "Switch Loadbalance")
		fmt.Println(colorRed("s"), "Status")
		fmt.Println(colorRed("f"), "Switch delay filter")
		fmt.Println(colorRed("v"), "Switch debug mode")
		fmt.Println(colorRed("r"), "Switch Fast mode")
		fmt.Println(colorRed("w"), "Start watch once")
		fmt.Println(colorRed("t"), "test and knocking [t xxx.x..]")
		fmt.Println(colorRed("e"), "Exit")

	TStart:
		count = 0
		for {
			count += 1
			userCommand = ""
			fmt.Printf("\nenter ip/name/url: ")
			userCommand, _ = screenReader.ReadString('\n')
			if ' ' == userCommand[0] || '\'' == userCommand[0] {
				userCommand = "0"
			}
			userCommand = strings.TrimSpace(userCommand)
			if len(userCommand) > 0 || count > 2 {
				break
			}
		}

		if 1 > len(userCommand) {
			continue
		}

		fmt.Println("COMMAND:", colorGreen(userCommand))

		//处理命令
		var t = checkUcmd(userCommand)
		switch t {
		case 1:
			delayFilter = !delayFilter
			continue
		case 2:
			//exit
			return
		case 3:
			//continue
			continue
		case 4:
			proxyFilter = userCommand
			continue
		}

		goto TStart
	}
}

func checkUcmd(userCommand string) int {
	switch true {
	case "c" == userCommand || 'c' == userCommand[0]:
		if len(userCommand) > 5 {
			testUrl = strings.TrimSpace(userCommand[1:])
		}
		if 'c' == userCommand[1] {
			//https://sbg.proof.ovh.net/files/1Mb.dat
			testUrl = "https://bom.proof.ovh.net/files/1Mb.dat"
		}
		fmt.Println("\nHealthCheck all...")
		go func() {
			s := tunnel.LoadOnlyProxies()
			for i := range s {
				//先清空历史记录
				s[i].CleanHistory()
			}
			hook.HealthCheckAll(nil, testUrl)
			manualUseFast()
		}()

	case "x" == userCommand:
		knockingOkAdapter = nil
		outboundgroup.AutoFastStrategyMode = 0
		outboundgroup.AutoFastReload()
		system.CloseAllConnections()

	case "r" == userCommand:
		c := outboundgroup.AutoFastStrategyMode + 1
		outboundgroup.AutoFastStrategyMode = c % 3
		outboundgroup.AutoFastReload()
		system.CloseAllConnections()

	case "a" == userCommand:
		system.SetAtAll("")

	case "f" == userCommand:
		return 1

	case "m" == userCommand:
		if tunnel.Mode() == tunnel.Global {
			tunnel.SetMode(tunnel.Rule)
		} else {
			tunnel.SetMode(tunnel.Global)
			fastGroup := system.GetGroupNameAutoFast()
			changeProxy(fastGroup.Name())
		}
		fmt.Println("Now Mode:", tunnel.Mode())

	case "v" == userCommand:
		level := log.ERROR
		switch log.Level() {
		case log.ERROR:
			level = log.DEBUG
			break

		case log.DEBUG:
			level = log.INFO
			break

		case log.INFO:
			level = log.ERROR
			break
		}
		envs.Debug = level != log.ERROR

		log.SetLevel(level)
		fmt.Println("Now Level:", level)

	case "s" == userCommand:
		snapshot := statistic.DefaultManager.Snapshot()
		n1, n2 := statistic.DefaultManager.Now()
		fmt.Println("Mode", tunnel.Mode())
		fmt.Println("DEBUG", envs.Debug)
		fmt.Println("DownloadTotal", snapshot.DownloadTotal)
		fmt.Println("UploadTotal", snapshot.UploadTotal)
		fmt.Println("Up:", n1, "Down:", n2)

	case "w" == userCommand:
		spider.StartWatchOnce()
		break

	case "e" == userCommand:
		if knockRunning {
			fmt.Println("knocking stop")
			return 2
		}
		manager.EventFinish()
		os.Exit(0)
		return 2

	case strings.HasPrefix(userCommand, "tt"):
		if "tt" == userCommand {
			userCommand = "m1"
		} else if "tta" == userCommand {
			userCommand = "m1 y01 y02"
		} else if len(userCommand) > 3 {
			userCommand = strings.TrimSpace(userCommand[3:])
			if adp := findProxy(userCommand, false); nil != adp {
				system.SetAtAll(adp.Name())
				knockingOkAdapter = adp
			}
		} else {
			userCommand = "m1"
		}
		go knocking(userCommand)

	case strings.Contains(userCommand, "//"):
		elm := resloveIp(userCommand)
		go knocking(elm)

	default:
		j, err := strconv.Atoi(userCommand)
		if nil == err {
			var name = ""
			n := len(showProxies)
			if n > 0 && n > j {
				name = showProxies[n-j-1].Name()
			} else {
				n = len(allProxies)
				if n > 0 && n > j {
					name = allProxies[n-j-1].Name()
				}
			}
			if len(name) > 1 {
				system.SetAtAll(name)
				knockingOkAdapter = nil
				system.CloseAllConnections()
				return 3
			}
		} else {
			if adp := findProxy(userCommand, false); nil != adp {
				system.SetAtAll(adp.Name())
				knockingOkAdapter = adp
				return 3
			}
		}

		return 4
	}
	return 0
}

func runAsGlobalMode() {
	tunnel.SetMode(tunnel.Global)
	fastGroup := system.GetGroupNameAutoFast()
	if nil != fastGroup {
		changeProxy(fastGroup.Name())
	}
}
func changeProxy(name string) {
	system.SetAtAll(name)
	fmt.Println("Change Proxy to:", name)
	//apiGlobal := apiURL + "/proxies/GLOBAL"
	//httplib.Put(apiGlobal).Body(fmt.Sprintf(`{"name":"%s"}`, name)).String()
}

func runAsAutoProxy() {
	st := time.Duration(autoCheckTime)

	for {
		LoadSortedProxies()
		hook.HealthCheckAll(allProxies, testUrl)
		name := allProxies[0].Name()
		changeProxy(name)

		time.Sleep(st * time.Minute)

	}
}

func dnsSetup() {
	net.DefaultResolver.PreferGo = true
	net.DefaultResolver.Dial = func(ctx context.Context, network, address string) (net.Conn, error) {
		d := net.Dialer{}
		return d.DialContext(ctx, "udp", "*******:53")
	}
}

func main() {
	printVersion()
	if version {
		return
	}

	fmt.Println("XClash:", runtime.GOOS)

	if runtime.GOOS == "linux" {
		dnsSetup()
	}

	// enable tls 1.3 and remove when go 1.13
	os.Setenv("GODEBUG", os.Getenv("GODEBUG")+",tls13=1")

	if homedir != "" {
		if !filepath.IsAbs(homedir) {
			currentDir, _ := os.Getwd()
			homedir = filepath.Join(currentDir, homedir)
		}
		C.SetHomeDir(homedir)
	} else {
		homedir, _ := os.Getwd()
		C.SetHomeDir(homedir)
	}

	if len(configFile) > 1 {
		C.SetConfig(configFile)
		envs.ConfigFile = configFile
	}

	//設置自動加載代理
	envs.Debug = isDebug
	envs.ResloveName = 3
	envs.MaxMngerProxy = 300
	hook.RegistToSystem()
	spider.AddRoot("./")
	fullSys.Setup(false)
	spider.AddFinish(func() {
		if len(allProxies) > 20 {
			return
		}
		envs.Reload()
	})

	if err := config.Init(C.Path.HomeDir()); err != nil {
		log.Fatalln("Initial configuration directory error: %s", err.Error())
	}

	//if err := hub.Parse(); err != nil {
	//	log.Fatalln("Parse config error: %s", err.Error())
	//}
	//
	//cfg, err := executor.Parse()
	//if err != nil {
	//	fmt.Println(err)
	//}

	if proxyType > 0 {
		if proxyType&1 > 0 {
			//不启动扫描器
			if proxyType&4 > 0 {
				manager.DisabledWatched()
			}
			go spider.StartAutoProxyServices()
		}
		if proxyType&2 > 0 {
			//go web.WebFinderStart()
		}
	}

	//apiURL = fmt.Sprintf("http://%s", cfg.General.ExternalController)
	//proxyURI = fmt.Sprintf("127.0.0.1:%d", cfg.General.SocksPort)

	if isDebug {
		log.SetLevel(log.DEBUG)
	}

	//启用http命令服务
	go startServer()

	go func() {
		for {
			if 1 > envs.Total && !envs.NoWatch {
				log.Warnln("Waiting for scan... %d", envs.Total)
				time.Sleep(2 * time.Second)
				continue
			}
			if err := hub.Parse(); err != nil {
				log.Fatalln("Parse config error: %s", err.Error())
			}

			cfg, err := executor.Parse()
			if err != nil {
				fmt.Println(err)
			}

			runAsGlobalMode()

			//apiURL = fmt.Sprintf("http://%s", cfg.General.ExternalController)
			proxyURI = fmt.Sprintf("127.0.0.1:%d", cfg.General.SocksPort)

			if runAsProxyServ {
				runAsAutoProxy()
			} else {
				//options()
				optionV2()
			}

			return
		}
	}()

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh
}

func _cmd_handler(w http.ResponseWriter, r *http.Request) {
	var c = r.URL.Query()
	var cmd = c.Get("c")
	if "0" == cmd {
		manualUseFast()
		return
	}
	checkUcmd(cmd)

	w.WriteHeader(http.StatusOK)
	fmt.Fprintln(w, "OK")
}

func startServer() {
	http.HandleFunc("/cmd", _cmd_handler)
	http.ListenAndServe(":7321", nil)
}
