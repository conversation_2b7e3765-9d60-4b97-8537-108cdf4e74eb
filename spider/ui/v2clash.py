#!/usr/bin/python3

import json,os,yaml
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import binascii
import requests,random,hashlib


class Scratch:

	def __init__(self):
		self.cc = "UDRnpNG4zVafoPDyKirGyqnq0gP4wlnS"
		self.dd = "UDRnpNG4zVafoPDyKirGyqnq0gP4wlnS"

	def decode(self, str, str2):
		return self.decrypt(self.parse_hex_str2_byte(str), str2)

	def parse_hex_str2_byte(self, str):
		if len(str) < 1:
			return bytearray()
		b_arr = bytearray(len(str) // 2)
		for i in range(len(str) // 2):
			i2 = i * 2
			i3 = i2 + 1
			b_arr[i] = (int(str[i2:i3], 16) * 16) + int(str[i3:i2 + 2], 16)
		return b_arr

	def get_key(self, str):
		return str.encode() if str else bytearray(24)


	def decrypt(self, b_arr, str):
		try:
			secret_key_spec = AES.new(self.get_key(str), AES.MODE_ECB)
			decrypted = unpad(secret_key_spec.decrypt(b_arr), AES.block_size)
			return decrypted
		except (ValueError, KeyError, TypeError) as e:
			print(e)
			return bytearray()


	def encode(self, str1, str2):
		encrypted_bytes = self.encrypt(str1, str2)
		if encrypted_bytes:
			return self.parse_byte2_hex_str(encrypted_bytes)
		else:
			return None

	def parse_byte2_hex_str(self, b_arr):
		stringBuffer = []
		for b in b_arr:
			hex_string = format(b & 0xFF, '02x')
			stringBuffer.append(hex_string.upper())
		return ''.join(stringBuffer)
 
	def encrypt(self, str1, str2):
		try:
			secret_key_spec = self.get_key(str2)
			cipher = AES.new(secret_key_spec, AES.MODE_ECB)
			bytes_to_encrypt = pad(str1.encode('utf-8'), AES.block_size)
			encrypted_bytes = cipher.encrypt(bytes_to_encrypt)
			return encrypted_bytes
		except Exception as e:
			print(e)
			return None


	def generate_random_string(self, length=32):
		characters = "123456789abcdef"
		result = []
		for s in range(length):
			random_index = int(random.random() * len(characters))
			result.append(characters[random_index])
		return ''.join(result)

	def main_update(self):
		headers = {
			'User-Agent': 'Mozilla/5.0 (Linux; Android 10; Pixel 3 Build/QQ3A.200805.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.86 Mobile Safari/537.36',
			'Content-Type': 'application/x-www-form-urlencoded',
		}
		imei = self.generate_random_string()
		url = 'https://api.appapistudio.xyz/node/getInformation_ex'
		text = '{"imei":"%s","platform":"android","version_number":35,"models":"Pixel 3","sdk":"29","m":"D2A5BCEE90DEAD36C4C817E97E6D2606","c":3}' % imei
		value = self.encode(text, self.dd)
		data = {'value': value}

		response = requests.post(url, headers=headers, data=data)
		return response.json()



	def export(self, tubev):
		# fn = "/Users/<USER>/data/data/tmp/delete/tubevpn/1111.json"
		# tubev = json.loads(open(fn).read())


		ssrs = tubev["goserverlist"]

		if 1 > len(ssrs):
			print("goserverlist empty")
			return

		"""
		{
			"name": "日本高速 1[普通]",
			"localPort": 1180,
			"host": "tacm.ix2.edge.kunlunae.com",
			"remotePort": 50120,
			"udphost": "tacm.ix2.edge.kunlunae.com",
			"udpport": 50120,
			"password": "mPQ5C9",
			"protocol": "auth_chain_a",
			"protocol_param": "45151944:LnGtzo",
			"obfs": "tls1.2_ticket_auth",
			"obfs_param": "",
			"method": "chacha20",
			"url_group": "无描述",
			"dns": "*******:53",
			"china_dns": "***************:53,*********:53",
			"status": 1,
			"country": "SG",
			"node_type_id": 1
		},
		"""

		clash = {
			"port": 8080,
			"socks-port": 7070,
			"allow-lan": True,
			"mode": "Global",
			"log-level": "debug",
			"external-controller": "127.0.0.1:9090",

			"watch": [{ "type": "clean" }],
		}


		proxies = []
		for m in ssrs:
			t = {
				"name": m["name"],
				"type": "ssr",
				"server": m["host"],
				"port": m["remotePort"],
				"password": m["password"],
				"cipher": m["method"],
				"obfs": m["obfs"],
				"obfs-param": m["obfs_param"],
				"protocol": m["protocol"],
				"protocol-param": m["protocol_param"],
				"udp": m["udpport"] > 0,
			}
			proxies.append(t)
			print(m["name"])


		clash["proxies"] = proxies


		with open('tubevpn.yaml', 'w', encoding='utf-8') as f:
			yaml.dump(data=clash, stream=f, allow_unicode=True)


	def main(self):
		t = self.main_update()

		data = self.decode(t["data"], self.dd)
		item = json.loads(data)

		self.export(item)

		# with open(ff, 'w') as file:
		# 	file.write(data.decode('utf-8'))

		print("=====Finish====>")

	def test1(self):
		self.main_update()

	def test(self):
		nn = "/tmp/33.txt"
		ff = nn.replace(".txt", ".json")
		with open(nn, 'r') as file:
			rr = file.read()
		ww = self.decode(rr, self.dd)
		print(ww)
		# with open(ff, 'w') as file:
		# 	  file.write(ww.decode('utf-8'))

if __name__ == "__main__":
	scratch = Scratch()
	scratch.main()
	# scratch.test1()


