package main

import (
	"fmt"
	"net/url"
)

//	func main() {
//		for {
//			mmmm()
//		}
//	}
func mmmm() {
	//uu := "https://raw.githubusercontent.com/oslook/clash-freenode/main/clash.yaml"
	//uu = "https://cdn.jsdelivr.net/gh/oslook/clash-freenode@main/clash.yaml"
	//uu = "https://github.com/oslook/clash-freenode/blob/main/clash.yaml"
	//
	//c := http.CdnParse(uu)
	//
	//fmt.Println(c.Jsdelivr())
	//fmt.Println(c.Sourcegraph())

	aa := "trojan://5bae27f5-3b8e-48f3-b91f-30fc680ea78f@*************:443?allowInsecureHostname=1&allowInsecureCertificate=1&allowInsecure=1&sni=lienquan.garena.vn#%E8%B6%8A%E5%8D%97-1.44MB/s"
	fmt.Scanln(&aa)

	fmt.Println(aa)

	bb, err := url.Parse(aa)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println("\n\n\n")
	fmt.Printf("===> %#v\n", bb)
	fmt.Printf("===> %#v\n", bb.User)
}

//func ttt() {
//
//	net.DefaultResolver.PreferGo = true
//	net.DefaultResolver.Dial = CloudflareDialer
//
//	httpGet("http://pv.sohu.com/cityjson")
//	httpGet("http://ip-api.com/json/")
//}
//
//
//func CloudflareDialer(ctx context.Context, network, address string) (net.Conn, error) {
//	d := net.Dialer{}
//	return d.DialContext(ctx, "udp", "*******:53")
//}
//func httpGet(url string) {
//	fmt.Println(url)
//	resp, err := http.Get(url)
//	if err != nil {
//		fmt.Println(err)
//	}
//
//	defer resp.Body.Close()
//	body, err := ioutil.ReadAll(resp.Body)
//	if err != nil {
//		fmt.Println(err)
//	}
//
//	fmt.Println(string(body))
//	fmt.Println("---")
//}
