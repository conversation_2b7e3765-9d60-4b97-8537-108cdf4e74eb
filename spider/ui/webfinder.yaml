# HTTP 代理端口
port: 8787

# SOCKS5 代理端口
socks-port: 7272

# Linux 和 macOS 的 redir 代理端口 (如需使用此功能，请取消注释)
# redir-port: 7892

# 允许局域网的连接（可用来共享代理）
allow-lan: true

# 规则模式：Rule（规则） / Global（全局代理）/ Direct（全局直连）
mode: Global

# 设置日志输出级别 (默认级别：info，级别越高日志输出量越大，越倾向于调试)
# 四个级别：info / warning / error / debug
log-level: info

# clash 的 RESTful API
external-controller: 127.0.0.1:9292


Proxy:
  - { name: "QMSS", type: socks5, server: 127.0.0.1, port: 7071 }
  - { name: "fake", type: socks5, server: 127.0.0.1, port: 1230 }

Proxy Group:
  #  AutoFast 是Rule模式下的快速选择
  #  - { name: "group1", type: autofast, url: "https://www.bing.com", interval: 300 , proxies: [QMSS] }
  - { name: "group2", type: fallback, url: "https://www.bing.com", interval: 300 , proxies: [fake] }
# - { name: "Proxy", type: url-test, proxies: ["http1","http2","http3","http4","http5","http6"], url: "https://www.bing.com", interval: 300 }


Rule:
  # Apple 服务优化
  ## 其他服务

  # 最终规则
  #- GEOIP,CN,DIRECT
  - MATCH,group2



#watch:
#  - {type: ss64, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/ss-sub"}
#  - {type: ss64, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/ssrsub"}
#  - {type: ss64, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/v2ray"}
#  - {type: surge, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/Surge.conf"}
#  - {type: clash, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/Clash.yml"}
#  - {type: ss64, url: "https://raw.githubusercontent.com/voken100g/AutoSSR/master/online"}
#  - {type: ss64, url: "https://raw.githubusercontent.com/eycorsican/rule-sets/master/kitsunebi_sub"}
#  - {type: ss64, url: "https://youlianboshi.netlify.app/"}
#  - {type: ss64, url: "http://ss.pythonic.life/full/subscribe"}
#  - {type: ss64, url: "https://prom-php.herokuapp.com/cloudfra_ssr.txt"}
#  - {type: ss, url: "https://t.me/s/ssrList"}
#  - {type: ss, url: "https://t.me/s/SSRSUB"}





finder:
  checker:
    - "http://www.jingyu.com/qhApi/bookinfo?bid=MtPdDRazMnz3OD"
    - "http://lakeshire.platform.zongheng.com/v2/book/get_book_info?appid=10014&bookid=595936121&sign=3a1e6fc7680a4a5eae83bb03e4584f24"

  tasks:
    - https://www.socks-proxy.net/

    - https://phantomjscloud.com/api/browser/v2/ak-tx2v7-4kd09-ydarg-kje5b-hqs7b/?request={url:%22http://www.proxynova.com/proxy-server-list/country-cn/%22,renderType:%22plainText%22}
    - https://phantomjscloud.com/api/browser/v2/ak-tx2v7-4kd09-ydarg-kje5b-hqs7b/?request={url:%22http://www.proxynova.com/proxy-server-list/country-us/%22,renderType:%22plainText%22}
    - https://phantomjscloud.com/api/browser/v2/ak-tx2v7-4kd09-ydarg-kje5b-hqs7b/?request={url:%22http://www.proxynova.com/proxy-server-list/country-hk/%22,renderType:%22plainText%22}
    - https://phantomjscloud.com/api/browser/v2/ak-tx2v7-4kd09-ydarg-kje5b-hqs7b/?request={url:%22http://www.proxynova.com/proxy-server-list/country-tw/%22,renderType:%22plainText%22}

    - http://service.prerender.io/http://www.gatherproxy.com/
    - http://service.prerender.io/http://www.gatherproxy.com/proxylist/country/?c=China&requestType=text

    - http://www.xicidaili.com/
    - http://www.xicidaili.com/nn/
    - http://www.xicidaili.com/nt/
    - http://www.xicidaili.com/wn/
    - http://www.xicidaili.com/wt/

    - http://v.daili666.com/ip/?tid=464799474710563&num=100
    - http://v.daili666.com/ip/?tid=558045424788230&num=100
    - http://66ip.cn/nmtq.php?getnum=100&isp=0&anonymoustype=4&start=&ports=&ipaddress=&area=0&proxytype=0&proxytype=1&api=71daili

    - http://www.kxdaili.com/dailiip/1/1.html
    - http://www.kxdaili.com/dailiip/1/2.html
    - http://www.kxdaili.com/dailiip/1/3.html
    - http://www.kxdaili.com/dailiip/1/4.html
    - http://www.kxdaili.com/dailiip/1/5.html
    - http://www.kxdaili.com/dailiip/1/6.html
    - http://www.kxdaili.com/dailiip/1/7.html
    - http://www.kxdaili.com/dailiip/1/8.html
    - http://www.kxdaili.com/dailiip/1/9.html
    - http://www.kxdaili.com/dailiip/1/10.html

    - http://www.ip181.com/
    - http://www.ip181.com/daili/1.html
    - http://www.ip181.com/daili/2.html
    - http://www.ip181.com/daili/3.html
    - http://www.ip181.com/daili/4.html
    - http://www.ip181.com/daili/5.html
    - http://www.ip181.com/daili/6.html
    - http://www.ip181.com/daili/7.html
    - http://www.ip181.com/daili/8.html
    - http://www.ip181.com/daili/9.html
    - http://www.ip181.com/daili/10.html


    - http://www.ip-adress.com/proxy_list/************:8080
    - http://service.prerender.io/http://nntime.com

    - https://www.hide-my-ip.com/proxylist.shtml

    - http://www.66ip.cn/nmtq.php?getnum=&isp=0&anonymoustype=3&start=&ports=&export=&ipaddress=&area=0&proxytype=2&api=66ip


    - https://www.us-proxy.org/
    - https://free-proxy-list.net/
    - https://free-proxy-list.net/uk-proxy.html
    - https://free-proxy-list.net/anonymous-proxy.html
    - http://www.66ip.cn/nmtq.php?getnum=1000&anonymoustype=%s&proxytype=2&api=66ip

    - http://service.prerender.io/http://proxy-list.org/english/index.php?s&pp=any&pt=any&pc=any&ps=any&p=1
    - http://service.prerender.io/http://proxy-list.org/english/index.php?s&pp=any&pt=any&pc=any&ps=any&p=2
    - http://service.prerender.io/http://proxy-list.org/english/index.php?s&pp=any&pt=any&pc=any&ps=any&p=3
    - http://service.prerender.io/http://proxy-list.org/english/index.php?s&pp=any&pt=any&pc=any&ps=any&p=4
    - http://service.prerender.io/http://proxy-list.org/english/index.php?s&pp=any&pt=any&pc=any&ps=any&p=5

    - http://www.proxyserverlist24.top/#follow@http://#proxy-server-list#.html
    - http://www.sslproxies24.top/#follow@http#-proxies-\d+.html
    - http://www.live-socks.net/#follow@http://#socks-5-servers-#.html
    - http://proxy-daily.com/#follow@http://#-proxy-list#/
    - http://proxy.highbroadcast.com/#follow@http#-proxies-\d+.html
    - https://sock5list.ml/#follow@http#proxies-\d+/
    - https://pirate.cloud/blog/#follow@http#proxy-\d+/


    - https://www.us-proxy.org/
    - https://www.sslproxies.org/
    - https://free-proxy-list.net/
    - https://free-proxy-list.net/web-proxy.html
    - https://free-proxy-list.net/anonymous-proxy.html
    - http://bestproxysites24.blogspot.com/
    - http://www.live-socks.net/
    - http://www.gatherproxy.com/sockslist/country/?c=Singapore
    - https://www.vpnuniversity.com/best/socks5-proxy

    - http://service.prerender.io/https://ip.ihuan.me/
    - http://service.prerender.io/https://ip.ihuan.me/
    - http://service.prerender.io/https://ip.ihuan.me/
    - http://service.prerender.io/https://ip.ihuan.me/

  notworks:
    - https://www.inforge.net/xi/forums/liste-proxy.1118/
    - http://www.proxyfire.net/forum/showthread.php?t=77513
    - http://www.sevgiforum.net/f150/#follow@#-t\d+.html


#  test:
    #  - https://t.me/s/ssrinter#ssr
    #  - https://raw.githubusercontent.com/ssrsub/ssr/master/ssrsub#base64#ssr
    #  - https://raw.githubusercontent.com/ssrsub/ssr/master/ss-sub#base64#ssr
    #  - https://htmlweb.ru/json/proxy/get
    #  - https://pirate.cloud/blog/#follow@/daily-free-proxy-\d+#
    #  - https://pirate.cloud/blog/page/2/#follow@/daily-free-proxy-\d+#
    #  - https://www.dailyfreeproxy.com
    #  - http://www.hide-my-ip.com/proxylist.shtml
    #  - http://sslproxies24.blogspot.jp/2016/10/03-10-16-free-google-proxies-140_3.html

    #  - http://www.proxynova.com/proxy-server-list/country-cn/
#    - ./socksth.txt

  #  - http://service.prerender.io/http://www.gatherproxy.com/proxylist/country/?c=China&requestType=text
  #https://www.google.com.tw/search?safe=strict&ei=OdtMXcX6EsKC-QaT4qSQDw&q=*************%3A38090&oq=*************%3A38090&gs_l=psy-ab.12...18241444.18241444..18242771...0.0..0.151.151.0j1......0....2j1..gws-wiz.15v3vCfwzj0&ved=










