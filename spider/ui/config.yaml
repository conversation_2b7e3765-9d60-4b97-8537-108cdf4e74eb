# 2018-10-11 00:15:14
#---------------------------------------------------#
## 配置文件需要放置在 $HOME/.config/clash/config.yml
##
## 对于 macOS 用户，如果你不知道如何操作，请将一下命令复制到 终端 （包括最后的 . 符号）并执行：
## mkdir -p $HOME/.config/clash/ && cd $HOME/.config/clash/ && sudo curl -o ./config.yml https://raw.githubusercontent.com/Hackl0us/SS-Rule-Snippet/master/LAZY_RULES/clash.yml -k -s && sudo chmod 775 ./config.yml && open .
## 在 Finder 弹出窗口中，打开并编辑 config.yml 即可。
#---------------------------------------------------#

# HTTP 代理端口
port: 8181

# SOCKS5 代理端口
socks-port: 7171

# Linux 和 macOS 的 redir 代理端口 (如需使用此功能，请取消注释)
# redir-port: 7892

# 允许局域网的连接（可用来共享代理）
allow-lan: true

# 规则模式：Rule（规则） / Global（全局代理）/ Direct（全局直连）
mode: Global

# 设置日志输出级别 (默认级别：info，级别越高日志输出量越大，越倾向于调试)
# 四个级别：info / warning / error / debug
log-level: debug

# clash 的 RESTful API
external-controller: 127.0.0.1:9191


proxies:
  - { name: "*", type: socks5, server: 127.0.0.1, port: 7071 }
#  - {name: hk2, server: hkt-a.synr.asia, port: 13114, type: ssr, cipher: aes-256-ctr, password: mimemi, protocol: auth_aes128_md5, protocolparam: 1:Wonderland0304, obfs: tls1.2_ticket_auth, obfsparam: hk2schab19f1.wns.windows.com}
#  - {name: hk1, server: hgc.synr.asia, port: 7008, type: ssr, cipher: aes-256-ctr, password: mimemi, protocol: auth_aes128_md5, protocolparam: 1:Wonderland0304, obfs: tls1.2_ticket_auth, obfsparam: hk2schab19f1.wns.windows.com}
#- { name: "http1", type: http, server: hkbn3.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
#- { name: "http2", type: http, server: hkt5.ghelper.net, port: 1443, tls: true, skip-cert-verify: true }
#- { name: "http3", type: http, server: us02.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
#- { name: "http4", type: http, server: hkbn3.ghelper.net, port: 2443, tls: true, skip-cert-verify: true }
#- { name: "http5", type: http, server: www.copyplay.net, port: 3389, tls: true, skip-cert-verify: true }
#- { name: "http6", type: http, server: www.copyplay.net, port: 1443, tls: true, skip-cert-verify: true }
#
#
#- { name: "http7", type: http, server: hkt.ghelper.net, port: 3389, tls: true, skip-cert-verify: true }
#- { name: "http8", type: http, server: www.copyplay.net, port: 3389, tls: true, skip-cert-verify: true }
#- { name: "http9", type: http, server: hkt5.ghelper.net, port: 3389, tls: true, skip-cert-verify: true }
#
#
#- { name: "QM-HK", type: ss, server: *************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
#- { name: "QM-KR", type: ss, server: *************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
#- { name: "QM-SG", type: ss, server: **************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
#- { name: "QM-SV", type: ss, server: ************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
#- { name: "QM-Auto", type: ss, server: vpn.ushow.media, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }

# # - { name: "QM-HK-1", type: ss2, server: *************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-2", type: ss, server: **************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-3", type: ss, server: **************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-4", type: ss, server: **************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-5", type: ss, server: ************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-6", type: ss, server: *************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-7", type: ss, server: ***************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }
# - { name: "QM-HK-8", type: ss, server: **************, port: 8388, cipher: AES-256-CFB, password: "x2w3FBkT" }

#<@>#
# - { name: "us01_ghelper_net_1443", type: http, server: us01.ghelper.net, port: 1443, tls: true, skip-cert-verify: true }
# - { name: "sg_ghelper_net_443", type: http, server: sg.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
# - { name: "ru2_ghelper_net_443", type: http, server: ru2.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
# - { name: "hkt4_ghelper_net_3389", type: http, server: hkt4.ghelper.net, port: 3389, tls: true, skip-cert-verify: true }
# - { name: "www_copyplay_net_3389", type: http, server: www.copyplay.net, port: 3389, tls: true, skip-cert-verify: true }
# - { name: "us02_ghelper_net_443", type: http, server: us02.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
# - { name: "de_ghelper_net_443", type: http, server: de.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
# - { name: "tw_ghelper_net_443", type: http, server: tw.ghelper.net, port: 443, tls: true, skip-cert-verify: true }
# - { name: "hkt5_ghelper_net_3389", type: http, server: hkt5.ghelper.net, port: 3389, tls: true, skip-cert-verify: true }
# - { name: "hkt6_ghelper_net_3389", type: http, server: hkt6.ghelper.net, port: 3389, tls: true, skip-cert-verify: true }

# shadowsocks
# 所支持的加密方式与 go-shadowsocks2 保持一致
# 支持加密方式： AEAD_AES_128_GCM AEAD_AES_192_GCM AEAD_AES_256_GCM AEAD_CHACHA20_POLY1305 AES-128-CTR AES-192-CTR AES-256-CTR AES-128-CFB AES-192-CFB AES-256-CFB CHACHA20-IETF XCHACHA20
# clash 额外支持 chacha20 rc4-md5 xchacha20-ietf-poly1305 加密方式

# - { name: "ss1", type: ss, server: server, port: 443, cipher: AEAD_CHACHA20_POLY1305, password: "password" }
# - { name: "ss2", type: ss, server: server, port: 443, cipher: AEAD_CHACHA20_POLY1305, password: "password", obfs: tls, obfs-host: bing.com }

# vmess
# 支持加密方式：auto / aes-128-gcm / chacha20-poly1305 / none
# - { name: "vmess1", type: vmess, server: server, port: 443, uuid: uuid, alterId: 32, cipher: auto }
# - { name: "vmess2", type: vmess, server: server, port: 443, uuid: uuid, alterId: 32, cipher: auto, tls: true }

# socks5
# - { name: "socks", type: socks5, server: server, port: 443 }
proxy-groups:
  - name: 自动选择
    type: autofast
    url: https://www.google.com/#
    interval: 300
    tolerance: 150
    proxies: ["*"]

#Proxy Group:
#  # url-test 可以自动选择与指定 URL 测速后，延迟最短的服务器
#  #- { name: "auto01", type: url-test, proxies: ["ru2_ghelper_net_443", "de_ghelper_net_443", "hkt6_ghelper_net_3389"], url: "https://www.bing.com", interval: 300 }
#  # - { name: "auto", type: url-test, proxies: ["ss1", "ss2", "vmess1"], url: "https://www.bing.com", interval: 300 }
#
#  # fallback 可以尽量按照用户书写的服务器顺序，在确保服务器可用的情况下，自动选择服务器
#  # - { name: "fallback-auto", type: fallback, proxies: ["ss1", "ss2", "vmess1"], url: "https://www.bing.com", interval: 300 }
#
#  # select 用来允许用户手动选择 代理服务器 或 服务器组
#  # 您也可以使用 RESTful API 去切换服务器，这种方式推荐在 GUI 中使用
#  #  AutoFast 是Rule模式下的快速选择
#  #  - { name: "group1", type: autofast, url: "https://www.bing.com", interval: 300 , proxies: [QMSS] }
#  - { name: "group2", type: autofast, url: "https://www.bing.com", interval: 300 , proxies: ["*"] }
## - { name: "Proxy", type: url-test, proxies: ["http1","http2","http3","http4","http5","http6"], url: "https://www.bing.com", interval: 300 }


Rule:
  # Apple 服务优化
  ## 其他服务

  # 最终规则
  #- GEOIP,CN,DIRECT
  - MATCH,自动选择



watch:
  - {type: clean}
  - {type: ss64, url: "https://jpcdn.chrpay.cn/s/57d5492d-b04e-4be4-ab70-c9576163c622"}
  # - {type: ss, url: "https://t.me/s/freeshadowsock"}
  # - {type: ss64, url: "https://raw.githubusercontent.com/learnhard-cn/free_proxy_ss/main/free"}
  # - {type: ss64, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/ss-sub"}
  # - {type: ss64, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/ssrsub"}
  # - {type: ss64, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/v2ray"}
  # - {type: surge, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/Surge.conf"}
  # - {type: clash, url: "https://raw.githubusercontent.com/ssrsub/ssr/master/Clash.yml"}
  # - {type: clash, url: "https://proxypoolsstest.herokuapp.com/clash/proxies"}
  # - {type: ss64, url: "https://raw.githubusercontent.com/voken100g/AutoSSR/master/online"}
  # - {type: ss64, url: "https://raw.githubusercontent.com/eycorsican/rule-sets/master/kitsunebi_sub"}
  # - {type: ss64, url: "https://youlianboshi.netlify.app/"}
  # - {type: ss64, url: "http://ss.pythonic.life/full/subscribe"}
  # - {type: ss64, url: "https://prom-php.herokuapp.com/cloudfra_ssr.txt"}
  # - {type: ss64, url: "https://proxypoolsstest.herokuapp.com/sip002/sub"}
#  - {type: ss, url: "https://t.me/s/ssrList"}
  # - {type: ss, url: "https://t.me/s/appmew"}
  # - {type: ss, url: "https://t.me/s/ShareCentre"}
  # - {type: ss, url: "https://t.me/s/SSRSUB"}
  # - {type: ss, url: "https://github.com/Alvin9999/new-pac/wiki/ss%E5%85%8D%E8%B4%B9%E8%B4%A6%E5%8F%B7"}
  # - {type: web, url: "https://fanqiangdang.com/forum-36-1.html##ss##http[^\"]+/thread[^\"]+.html"}
  # - {type: web, url: "https://fanqiangdang.com/forum-2-1.html##ss##http[^\"]+/thread[^\"]+.html"}
  # - {type: web, url: 'https://freefq.com/free-ssr/##ss@http[^"]+/[a-z0-9]{20,}.htm##web@/free-ssr/\d+[^"]+html'}
  # - {type: ss, url: "https://fanqiangdang.com/thread-19775-1-1.html"}





