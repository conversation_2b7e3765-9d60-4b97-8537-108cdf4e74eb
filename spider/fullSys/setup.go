package fullSys

import (
	"clash-foss/component/mmdb"
	"clash-foss/spider/envs"
	"net"
)

func Setup(reloader bool) {
	envs.ResloveNameFunc = func(s string) string {
		ipnet := net.ParseIP(s)
		record, err := mmdb.Instance().Country(ipnet)

		if err != nil {
			return ""
		}
		if envs.IsChinese {
			if c, ok := record.Country.Names["zh-CN"]; ok {
				return c
			}
		}

		if c, ok := record.Country.Names["en"]; ok {
			return c
		}

		return ""
	}

	//重新加载
	/* * /
	if reloader {
		envs.DefaultReLoader = func() {
			force := false
			var err error
			var cfg *config.Config

			dd := data.RawConfig()
			if nil != dd {
				cfg, err = executor.ParseWithBytes(dd)
			} else {
				cfg, err = executor.ParseWithPath(envs.ConfigFile)
				if err != nil {
					cfg, err = executor.ParseWithPath(C.Path.Config())
				}
				if err != nil {
					log.Warnln("reload %s", err.Error())
					return
				}
			}

			executor.ApplyConfig(cfg, force)

			//system.CloseAllConnections()
			//system.AutoChooseFastSigleProxy()
		}
	}
	//*/
}
