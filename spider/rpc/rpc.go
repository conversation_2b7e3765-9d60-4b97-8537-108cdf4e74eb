package rpc

import (
	"clash-foss/adapter"
	"clash-foss/adapter/outboundgroup"
	"clash-foss/log"
	"clash-foss/spider"
	holdData "clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/fullSys"
	"clash-foss/spider/http"
	"clash-foss/spider/manager"
	"clash-foss/spider/misc"
	"clash-foss/spider/system"
	"clash-foss/spider/types"
	"clash-foss/tunnel"
	"encoding/json"
	"strconv"
)

func Rpc(act int, uri string) string {
	var data []byte = nil

	switch act {
	case 1: //请求内部加密API
		data = http.SubGet(uri)
		break
	case 2: //檢查配置信息
		return manager.CheckConfigOnLine()
		break

	case 3: //添加外部代理
		holdData.AddWatch(uri)
		break

	case 5: //添加代理
		holdData.AddProxy(uri)
		break

	case 6: //返回最快的代理
		data = getFastProxyName()
		break

	case 7: //当前代理
		data = getCurrentProxy()
		break

	case 8: //启动查找器
		if !spider.ISRunning() {
			spider.MobileInit()
			go misc.SafeGo(spider.StartAutoProxyServices)
		}
		break

	case 9: //解释国家名字
		return system.ResloveCountry(uri)
		break

	case 10: //自动切换最快代理
		return system.AutoChooseFastSigleProxy()
		break

	case 11: //设置主目录
		spider.AddRoot(uri)
		//envs.AddRoot(uri)
		break

	case 12: //用代理读取网址
		data = http.HttpGet(uri)
		break

	case 13: //当前可用代理
		return strconv.Itoa(envs.Total)
		break

	case 15: //设置所有代理
		system.SetAtAll(uri)
		break

	case 16: //加載UI信息
		return envs.UMes
		break

	case 17: //重新加载配置
		envs.Reload()
		break

	case 18: //关闭当前连接
		system.CloseAllConnections()
		break

	case 19: //设置语言
		envs.IsChinese = types.ConvInt(uri) > 0
		break

	case 20: //设置完整系统
		//包括IP位置识别和配置文件重载等
		fullSys.Setup(false)
		break

	case 21: //切换全局/规则模式
		mode := types.ConvInt(uri)
		switch mode {
		case 1:
			tunnel.SetMode(tunnel.Rule)
			break
		case 2:
			tunnel.SetMode(tunnel.Global)
			break
		default:
			return ""
		}
		system.SetAtAll("")
		system.CloseAllConnections()
		break

	case 22: //切换AutoFast的模式
		mode := types.ConvInt(uri)
		outboundgroup.AutoFastStrategyMode = mode
		outboundgroup.AutoFastReload()
		system.CloseAllConnections()
		break

	case 23: //自动规则的名称
		elm := system.GetGroupNameAutoFast()
		if nil != elm {
			return elm.Name()
		}
		break

	case 846: //开启调试模式
		envs.Debug = true
		log.SetLevel(log.DEBUG)
		break
	}

	if nil == data {
		return ""
	}

	return string(data)
}

func getCurrentProxy() []byte {
	ps := tunnel.Proxies()

	items := make(map[string]string)

	for _, p := range ps {
		pi, ok := p.(*adapter.Proxy)
		if !ok {
			continue
		}

		group, ok := pi.ProxyAdapter.(outboundgroup.ProxyGroup)
		if !ok {
			continue
		}

		//items[group.Name()] = []string{group.Now(), group.Type().String()}
		items[group.Name()] = group.Now()
	}

	data, _ := json.Marshal(items)

	return data
}

func getFastProxyName() []byte {
	size := 10
	proxies := tunnel.LoadSortedProxies(size)
	total := len(proxies)
	if nil == proxies || 1 > total {
		return nil
	}

	if total > size {
		total = size
	}

	items := make([]string, total)
	for i := 0; total > i; i++ {
		m := proxies[i]
		items[i] = m.Name()
	}

	data, _ := json.Marshal(items)
	return data
}
