package roxyp

import (
	"clash-foss/spider/rpc"
	"clash-foss/spider/system"
	"clash-foss/spider/types"
	"fmt"
)

func Rps(act int) {
	Rpc(act, "")
}

//调用
func Rpc(act int, argv string) string {

	switch act {

	//启动代理服务
	case 20:
		start_proxy()
		break

	//设置端口
	case 21:
		v := types.ConvInt(argv)
		if v > 0 {
			port = v
		}
		break

	//重置及重新加载配置等
	case 22:
		resetAll()
		break

	case 25:
		//stopService()
		break

	//设置默认代理
	case 26:
		system.SetAtAll(defaultGroup)
		break

	case 846: //是否DEBBUG
		debug()
		break

	default:
		if act > 100 {
			//某些act可能被覆盖
			//用此方法调用背后的功能
			act = act - 100
		}
		return rpc.Rpc(act, argv)

	}

	return fmt.Sprintf("%d", act)
}
