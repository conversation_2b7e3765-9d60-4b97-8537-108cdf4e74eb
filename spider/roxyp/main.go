package roxyp

import (
	"clash-foss/config"
	"clash-foss/hub/executor"
	P "clash-foss/listener"
	"clash-foss/log"
	"clash-foss/spider/envs"
	"clash-foss/spider/hook"
	"clash-foss/spider/system"
	"clash-foss/tunnel/statistic"

	//P "clash-foss/proxy"
	"clash-foss/tunnel"
)

var (
	port         = 65239
	defaultGroup = "AutoFast"
)

//嵌入版本
//直接作为手机库使用

func resetAll() {
	loadDefault()
	statistic.DefaultManager.ResetStatistic()
}

func loadDefault() {
	var err error
	var defaultc *config.Config

	if nil != envs.ExtendConfig {
		defaultc, err = config.Parse(envs.ExtendConfig)
	} else {
		rawCfg := &config.RawConfig{
			AllowLan:       false,
			BindAddress:    "*",
			Mode:           tunnel.Global,
			Authentication: []string{},
			LogLevel:       log.ERROR,
			Hosts:          map[string]string{},
			Rule:           []string{},
			Proxy:          []map[string]interface{}{},
			ProxyGroup: []map[string]interface{}{{
				"name":     defaultGroup,
				"type":     "fast",
				"url":      "https://www.bing.com",
				"interval": envs.CheckIntervalSec,
				"proxies":  []interface{}{},
			}},
			Experimental: config.Experimental{
				//IgnoreResolveFail: true,
			},
			DNS: config.RawDNS{
				Enable:      false,
				FakeIPRange: "**********/16",
				FallbackFilter: config.RawFallbackFilter{
					GeoIP:  true,
					IPCIDR: []string{},
				},
				DefaultNameserver: []string{
					"**************",
					"*******",
					"*******",
					"**************",
					"***************",
				},
			},

			// remove after 1.0
			//RuleOld:       []string{},
			//ProxyOld:      []map[string]interface{}{},
			//ProxyGroupOld: []map[string]interface{}{},
		}
		//defaultc, err = config.ParseRawConfig(rawCfg, envs.Root)
		defaultc, err = config.ParseRawConfig(rawCfg)
	}

	if err != nil {
		log.Warnln("Load Default Failure " + err.Error())
		return
	}

	defaultc.General.Mode = tunnel.Global
	executor.ApplyConfig(defaultc, true)
}

func start_proxy() {

	log.SetLevel(log.ERROR)

	P.SetAllowLan(false)

	//data.LoadDiskCache()
	hook.RegistToSystem()

	loadDefault()

	envs.DefaultReLoader = loadDefault
	envs.TaskIntervalMinute = 90
	envs.CheckIntervalSec = 3600

	tunnel.SetMode(tunnel.Global)

	P.SetBindAddress("127.0.0.1")

	tcpIn := tunnel.TCPIn()
	udpIn := tunnel.UDPIn()

	//P.ReCreateHTTP(pointerOrDefault(general.Port, ports.Port))
	P.ReCreateSocks(port, tcpIn, udpIn)
	//P.ReCreateRedir(pointerOrDefault(general.RedirPort, ports.RedirPort))
	//P.ReCreateMixed(pointerOrDefault(general.MixedPort, ports.MixedPort))

	//proxies := tunnel.Proxies()
	//if proxy, exist := proxies["GLOBAL"].(*outbound.Proxy); exist {
	//	if selector, ok := proxy.ProxyAdapter.(*outboundgroup.Selector); ok {
	//		_ = selector.Set(defaultGroup)
	//	}
	//}
	system.SetAtAll(defaultGroup)

	//fmt.Println("start:", port, err)
}

func debug() {
	envs.Debug = true
	log.SetLevel(log.DEBUG)
}
