package manager

import (
	"clash-foss/adapter"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/hook"
	"clash-foss/spider/misc"
	"clash-foss/spider/system"
	"clash-foss/tunnel"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"clash-foss/log"
	"clash-foss/spider/types"
)

var (
	//taskerRunning = false
	//tasker        = make(chan <PERSON>asker)
	tmpncLocker sync.Mutex
	tmpdc       = make(map[string]int)
	cleanCdTime = time.Now()
)

func Save() {
	data.Save()
	go misc.SafeGo(func() {
		cleanUp(nil)
	})
}

func cleanUp(testFunc func()) {
	//后加载模式不需要清理
	if envs.LoadAfterChecked {
		return
	}

	if !envs.ISNetworkConned {
		return
	}
	now := time.Now()
	if cleanCdTime.After(now) {
		return
	}

	cleanCdTime = now.Add(time.Duration(30) * time.Minute)

	if nil == testFunc {
		speedTest()
	} else {
		testFunc()
	}
	tmp := system.GetDeadProxies()
	if nil == tmp {
		return
	}
	proxies := *tmp
	tmpncLocker.Lock()
	for name, _ := range proxies {
		if _, ok := tmpdc[name]; !ok {
			tmpdc[name] = 0
		}
		tmpdc[name] += 1
	}
	for name, c := range tmpdc {
		//如果此代理正常则重置计数
		if _, ok := proxies[name]; !ok {
			tmpdc[name] = 0
		}

		//统计错误次数才最终删除
		if c > envs.ProxyCleanerTimes {
			data.RemoveConfig(name)
			delete(tmpdc, name)
		}
	}
	//重新应用配置
	envs.Reload()
	tmpncLocker.Unlock()
}

// func CheckProxy(ctx context.Context, hashConfig map[string]interface{}) {
func CheckProxy(ctx context.Context, task ChkTasker) {
	if nil == task.Data || task.ErrorCount > 2 || envs.IsFull() {
		return
	}

	if nil == task.Config {
		task.Config = &data.HashPrxConfig{Config: task.Data}
	}

	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()

	host := task.Config.Host()
	hostId := task.Config.ProxyId()

	//isNotExpired := data.IsFlagExists(hostId)

	/*
		//所有配置信息都检查一次
		if data.IsFlagExists(host) {
			if task.AddWhenNotExp {
				if envs.ResloveName > 0 {
					task.Config.GenName()
				}
				data.AddConfig(task.Config)
			}
			return
		}

		if data.HasConfig(task.Config) {
			return
		}
		// */

	if envs.FastPing && !misc.Ping(host) {
		//if envs.Debug {
		//	log.Infoln("REPORT/SKIP %s", host)
		//}
		return
	}

	if envs.ResloveName > 0 {
		task.Config.GenName()
	}

	proxy, err := adapter.ParseProxy(task.Config.Config)
	if err != nil {
		if envs.Debug {
			log.Infoln("cs/parse %s %s", host, err.Error())
			fmt.Println("---PPPE1------->", task.Data["_uri"])
			fmt.Println("---PPPE2------->", task.Data["_uri0"])
		}
		return
	}

	//检查在代理池中是否存在
	if tunnel.HasProxy(proxy) {
		return
	}

	//严格模式，必须检查再添加
	//strict := 4 > tunnel.LoadProxiesSize()

	//如果没过期，而且代理来自保存的配置则直接加和代理池
	tunnel.AddNewProxy(proxy)
	//if isNotExpired && task.AddWhenNotExp {
	//	tunnel.AddNewProxy(proxy)
	//}

	jobAdd()
	defer func() {
		jobFinish()
	}()

	//手机优化
	//前N个代理只有Ping通就加入，加快搜索的速度
	if envs.UseInMobile && 10 > envs.Total {
		tunnel.AddNewProxy(proxy)
	}

	//3小時內過期
	data.FlagAdd(hostId)

	if nil == ctx {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(context.Background(), envs.DefaultURLTestTimeout)
		defer cancel()
	}

	//保证前N个代理必须可用
	//testUrl := "dt"
	//testUrl := "https://t.me/v/"

	t, _, err := proxy.URLTest(ctx, "c")
	//if err != nil && envs.Debug {
	//	log.Infoln("REPORT/test %s %s", proxy.Name(), err.Error())
	//}

	if t > 0 {
		data.AddConfig(task.Config)
		tunnel.AddNewProxy(proxy)
	} else if nil != err && hasRetry(err.Error()) {
		//测试失败则再试一次
		task.ErrorCount += 1
		ShareWorker <- WKTasker{Act: 1, Data: task}
	}
}
func hasRetry(emsg string) bool {
	return strings.Contains(emsg, "timeout") ||
		strings.Contains(emsg, "EOF") ||
		strings.Contains(emsg, "exceeded")
}

func CheckS(ssr string, ctx context.Context) {
	//mapping := types.ParseSSR(ssr)
	mapping := types.ParseSSRV2(ssr)

	if nil == mapping {
		return
	}
	//tasker <- ChkTasker{Data: mapping}
	ShareWorker <- WKTasker{Act: 1, Data: ChkTasker{Data: mapping}}

}

//func resloveName(host string, port int) string {
//	if envs.ResloveName > 0 {
//		tmp := system.ResloveCountry(host)
//		if len(tmp) > 1 {
//			ext := host
//			switch envs.ResloveName {
//			case 1:
//				tmpncLocker.Lock()
//				nc, ok := tmpnc[tmp]
//				if ok {
//					nc += 1
//				} else {
//					nc = 1
//				}
//				tmpnc[tmp] = nc
//				ext = strconv.Itoa(nc)
//				tmpncLocker.Unlock()
//				break
//			case 2:
//				break
//			case 3:
//				ext = fmt.Sprintf("%s-%d", host, port)
//				break
//			}
//
//			return fmt.Sprintf("%s[%s]", tmp, ext)
//		}
//	}
//	return fmt.Sprintf("%s-%d", host, port)
//}

func speedTest() {
	test1 := "https://www.google.com/#"
	proxies := tunnel.LoadOnlyProxies()
	hook.HealthCheckAll(proxies, test1)

}
