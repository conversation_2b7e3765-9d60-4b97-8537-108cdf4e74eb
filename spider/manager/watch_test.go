package manager

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"testing"
)

func TestDisabledWatched(t *testing.T) {
	//tests := []struct {
	//	name string
	//}{
	//	// TODO: Add test cases.
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		DisabledWatched()
	//	})
	//}
	basep := ""
	baseUrl := "https://freefq.com/free-ssr/"
	subUrl := "2021/04/04/ssrvpn.html"
	basex, _ := url.Parse(baseUrl)

	if !strings.HasPrefix(subUrl, "http") {
		if strings.HasPrefix(subUrl, "/") {
			subUrl = fmt.Sprintf("%s://%s%s", basex.Scheme, basex.Host, subUrl)
		} else {
			if 1 > len(basep) {
				v := strings.LastIndex(baseUrl, "/")
				basep = baseUrl[0 : v+1]
			}
			subUrl = basep + subUrl
		}
	}

	fmt.Println(subUrl)


	//finder := regexp.MustCompile(".(com.*)/")
	finder := regexp.MustCompile(".com.*/")

	fmt.Println("=========>")

	if matchs := finder.FindAllStringSubmatch(subUrl, -1); nil != matchs {

		fmt.Printf("== %#v\n",matchs)
		for _, tmp := range matchs {

			fmt.Println(tmp[0])
		}
	}

}
