package manager

import (
	"clash-foss/log"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/misc"
	"clash-foss/spider/types"
	"fmt"
	"gopkg.in/yaml.v3"
	"os"
)

var (
	isConfigLoad     = false
	isWatchTaskStart = false
	rawConfig        = &types.ExGeneralConfig{}
	delayTestStarted = false
)

func init() {
	WatchConfig0()
}

func loadConfig() {
	if isConfigLoad {
		return
	}
	loadFileConfig()
	if isConfigLoad {
		return
	}
	d := data.RawConfig()
	if nil == d {
		log.Warnln("Empty config skip")
		return
	}
	isConfigLoad = true
	err := yaml.Unmarshal(d, &rawConfig)
	if err != nil {
		log.Errorln("Config %s", err)
		return
	}
}

func loadFileConfig() {
	//直接加载文件
	bytes, err := os.ReadFile(envs.ConfigFile)
	if err != nil {
		log.Errorln(err.Error())
		return
	}

	if err = yaml.Unmarshal(bytes, &rawConfig); err != nil {
		log.Errorln("clash/load %s", err.Error())
	}
	isConfigLoad = true
	return
}

func StartHarfService() {
	startNetworkMonitor()
	loadConfig()
	data.LoadDiskCache()
	//StartDelayTest()
}
func AllServiceStart() {
	startNetworkMonitor()
	data.LoadDiskCache()
	StartShareWorker()
	loadConfig()
	//StartDelayTest()

	//启动扫描任务
	w := WatchService{}
	w.StartAll()
	startJobMonitor()
}

func StartWatchOnce() {
	//启动扫描任务
	w := WatchService{}
	w.StartAll()
}

func startJobMonitor() {
	if isWatchTaskStart {
		return
	}
	isWatchTaskStart = true

	//状态信息
	if envs.ShowSatus {
		misc.Add1MinuteTask(func() {
			log.Warnln(">>>STATUS<<< %s", fmt.Sprintf("%#v", data.Status))
		})
	}

	watcher := WatchService{}
	misc.Timer(envs.TaskIntervalMinute*60, func() {
		CheckConfigOnLine()
		watcher.StartAll()
	})
}

//func AddWatch(vtype, url string) {
//	loadConfig()
//	w := types.ExWatch{Type: vtype, Url: url}
//	rawConfig.Watch = append(rawConfig.Watch, w)
//}

func EventFinish() {
	if nil == envs.FinishCallback || 1 > len(envs.FinishCallback) {
		return
	}

	log.Infoln("All job finish")
	for _, fn := range envs.FinishCallback {
		fn()
	}
}

func startNetworkMonitor() {
	envs.ISNetworkConned = misc.Ping("qq.com:80")
	misc.Add1MinuteTask(func() {
		envs.ISNetworkConned = misc.Ping("qq.com:80")
	})
}

//func StartDelayTest() {
//	if delayTestStarted {
//		return
//	}
//	if !envs.ISNetworkConned {
//		return
//	}
//	delayTestStarted = true
//
//	go misc.SafeGo(func() {
//		time.Sleep(1 * time.Second)
//		proxies := system.LoadOnlyProxies()
//
//		if nil == proxies || 1 > len(*proxies) {
//			return
//		}
//
//		//檢查延遲
//		cleanUp(func() {
//			hook.HealthCheckAll(proxies, testurl)
//		})
//
//		//切換最快的代理
//		system.Reload()
//	})
//}
