package manager

import (
	config2 "clash-foss/config"
	"clash-foss/log"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/http"
	"clash-foss/spider/misc"
	"clash-foss/spider/types"
	"context"
	"encoding/base64"
	"fmt"
	"gopkg.in/yaml.v3"
	"math/rand"
	"net/url"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"
)

var (
	//ssrFinder regexp.MustCompile(`[svt][srojamenl]{1,6}://[A-Za-z0-9@+/=_\-:.#\\?&%]+`)
	ssrFinder = regexp.MustCompile(`[svt][aejlmnrso]{1,6}://\S+`)
	jobLocker = false
	jobCount  = 0
	_clash    = "clash"
	_surge    = "surge"
	_ss64     = "ss64"
	_vmess    = "vmess"
	_ss       = "ss"
	_web      = "web"
	skipCache = sync.Map{}

	watchLocker = sync.Mutex{}
)

type WatchService struct {
	watchWorker *misc.Worker[types.ExWatch]
}

func GetJobCount() int {
	return jobCount
}

func jobAdd() {
	jobCount += 1
}

func DisabledWatched() {
	envs.LoadAfterChecked = false
	jobLocker = true
}

func jobFinish() {
	jobCount -= 1
	if 0 == jobCount {
		//go EventFinish()
		go misc.SafeGo(EventFinish)
	}
}
func (this *WatchService) StartAll() {
	if jobLocker {
		return
	}
	watchLocker.Lock()
	defer watchLocker.Unlock()

	jobAdd()
	jobLocker = true
	//chromehelper()
	this.WatchConfig1()
	this.WatchConfig2()
	this.WatchConfig3()

	//如果可用代理过于少则再试一次
	if 30 > envs.Total {
		this.WatchConfig3()
	}

	jobLocker = false
	jobFinish()
}

func (this *WatchService) addMatchsProxies(matchs [][]byte) {
	ctx, cancel := context.WithTimeout(context.Background(), envs.DefaultURLTestTimeout)
	for _, ssr := range matchs {
		tmp := string(ssr)
		CheckS(tmp, ctx)
		//go misc.SafeGo(func() {
		//	CheckS(tmp, ctx)
		//})
		//ShareWorker <- WKTasker{Act: 9, Data: func() { CheckS(tmp, ctx) }}
	}

	<-ctx.Done()
	cancel()
}

func (this *WatchService) addClashConfigProxies(webbody []byte) int {

	rawCfg := &config2.RawConfig{}

	if err := yaml.Unmarshal(webbody, &rawCfg); err != nil {
		log.Errorln("clash/load %s", err.Error())
		return 0
	}

	proxies := rawCfg.Proxy

	//if nil == proxies {
	//	proxies = rawCfg.ProxyOld
	//}

	//ctx, cancel := context.WithTimeout(context.Background(), defaultURLTestTimeout)
	for j, mapping := range proxies {
		if nil == mapping {
			continue
		}
		mapping["name"] = fmt.Sprintf("cs_%s_%d", mapping["server"], mapping["port"])
		tmp := proxies[j]
		//go CheckProxy(ctx, &tmp)
		//tasker <- ChkTasker{Data: tmp}
		ShareWorker <- WKTasker{Act: 1, Data: ChkTasker{Data: tmp}}
	}

	//<-ctx.Done()
	//cancel()

	return len(proxies)
}

func (this *WatchService) addSugerConfigProxies(webbody []byte) int {
	lines := strings.Split(string(webbody), "\n")
	//ctx, cancel := context.WithTimeout(context.Background(), defaultURLTestTimeout)
	total := 0
	need := false

	for _, v := range lines {
		if 1 > len(v) {
			continue
		}
		if '[' == v[0] {
			if strings.Contains(v, "[Proxy]") {
				need = true
			} else {
				need = false
			}
		}
		if !need || !strings.Contains(v, ",") {
			continue
		}

		total += 1
		mapping := types.ParseSurge(v)
		//go CheckProxy(ctx, &mapping)
		//tasker <- ChkTasker{Data: mapping}
		ShareWorker <- WKTasker{Act: 1, Data: ChkTasker{Data: mapping}}
	}

	//<-ctx.Done()
	//cancel()

	return total
}

func (this *WatchService) Watch(watch types.ExWatch) {
	if nil == this.watchWorker {
		this.watchWorker = misc.NewWorker[types.ExWatch](envs.WatchWorkerNum)
		this.watchWorker.Start(this.doWatch)
	}
	this.watchWorker.Send(watch)
}
func (this *WatchService) doWatch(watch types.ExWatch) {
	//当代理已经足够多了就不再查找
	if envs.IsFull() && !watch.Force {
		return
	}

	jobAdd()
	defer func() {
		jobFinish()
	}()

	baseUrl := watch.Url
	if envs.Debug {
		log.Infoln("watch: %s", baseUrl)
	}

	var body []byte
	if strings.HasPrefix(baseUrl, "file:") {
		fn := strings.Split(baseUrl, "://")[1]
		body, _ = os.ReadFile(fn)
	} else {
		body = http.CdnGet(baseUrl)
	}
	if nil == body {
		watch.Status += 1
		if 5 > watch.Status {
			this.Watch(watch)
		}
		log.Errorln("watch skip %d, %s", watch.Status, baseUrl)
		return
	}

	total := 0

	switch watch.Type {
	case _clash:
		total = this.addClashConfigProxies(body)

	case _surge:
		total = this.addSugerConfigProxies(body)
	case _ss, _ss64, _vmess:
		if _ss64 == watch.Type {
			maxLen := base64.StdEncoding.DecodedLen(len(body))
			dst := make([]byte, maxLen)
			_, err := base64.StdEncoding.Decode(dst, body)
			if err != nil {
				log.Warnln("decode %s", err.Error())
			}
			if nil != dst {
				body = dst
			}
		}
		if matchs := ssrFinder.FindAll(body, -1); nil != matchs {
			total = len(matchs)
			this.addMatchsProxies(matchs)
		}
	case _web:
		spx := strings.Split(baseUrl, "##")
		for _, tmp := range spx[1:] {
			v := strings.Index(tmp, "@")
			this.findWebBody(tmp[0:v], baseUrl, tmp[v+1:], body)
		}
	}

	if envs.Debug {
		log.Infoln("Load config %s, size: %d", watch.Url, total)
	}
}
func (this *WatchService) findWebBody(watchType, baseUrl, rule string, body []byte) {
	parms := ""
	if t := strings.Index(baseUrl, "##"); t > 0 {
		parms = baseUrl[t:]
	}
	finder := regexp.MustCompile(rule)
	if matchs := finder.FindAllSubmatch(body, -1); nil != matchs {
		for _, tmp := range matchs {
			subUrl := string(tmp[len(tmp)-1])
			if !strings.HasPrefix(subUrl, "http") {
				if strings.HasPrefix(subUrl, "/") {
					basex, _ := url.Parse(baseUrl)
					subUrl = fmt.Sprintf("%s://%s%s", basex.Scheme, basex.Host, subUrl)
				} else {
					v := strings.LastIndex(baseUrl, "/")
					subUrl = baseUrl[0:v] + subUrl
				}
			}
			subUrl += parms

			if _, ok := skipCache.Load(subUrl); !ok {
				skipCache.Store(subUrl, true)
				this.Watch(types.ExWatch{Type: watchType, Url: subUrl})
			}
		}
	}
}
func (this *WatchService) WatchConfig1() {
	//添加预定义的代理
	if 1 > len(rawConfig.SSR) {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), envs.DefaultURLTestTimeout)
	for _, ssr := range rawConfig.SSR {
		tmp := ssr
		CheckS(tmp, ctx)
		//go misc.SafeGo(func() {
		//	CheckS(tmp, ctx)
		//})
		//ShareWorker <- WKTasker{Act: 9, Data: func() { CheckS(tmp, ctx) }}
	}
	<-ctx.Done()
	cancel()

}

func (this *WatchService) WatchConfig2() {
	for i := 0; i < 30; i++ {
		loadConfig()
		if nil == rawConfig || nil == rawConfig.Watch {
			log.Infoln("WatchConfig2 yaml empty. %d", i)
			time.Sleep(3 * time.Second)
			continue
		}
		break
	}

	if nil == rawConfig || nil == rawConfig.Watch {
		panic("WatchConfig2 yaml all empty.")
		return
	}
	if envs.Debug {
		log.Infoln("Start Load config", len(rawConfig.Watch))
	}
	for _, w := range rawConfig.Watch {
		if w.Type == "clean" {
			data.WatchList = make([]string, 0)
			continue
		}
		this.Watch(w)
	}
}
func (this *WatchService) WatchConfig3() {
	if nil == data.WatchList || 1 > len(data.WatchList) {
		envs.NoWatch = true
		return
	}

	//打乱顺序
	tmp := data.WatchList
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(tmp), func(i, j int) { tmp[i], tmp[j] = tmp[j], tmp[i] })

	for _, c := range tmp {
		j := strings.Index(c, "@")
		this.Watch(types.ExWatch{Type: c[0:j], Url: c[j+1:]})
	}

}

// 檢查緩存中的舊代理
func WatchConfig0() {
	if !envs.LoadAfterChecked {
		return
	}
	data.CheckStoreFunc = func(stores *data.StoreConfig) {
		if nil == stores || 1 > len(stores.Configs) {
			return
		}

		//watchLocker.Lock()
		//defer watchLocker.Unlock()

		for _, m := range stores.Configs {
			//tasker <- ChkTasker{Data: m, AddWhenNotExp: true}
			ShareWorker <- WKTasker{Act: 1, Data: ChkTasker{Data: m, AddWhenNotExp: true}}
		}

		envs.Reload()
	}
}
