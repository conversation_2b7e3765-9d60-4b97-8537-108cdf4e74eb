package manager

import (
	"clash-foss/spider/envs"
	"clash-foss/spider/misc"
)

var (
	shareWkRunning = false
	ShareWorker    = make(chan <PERSON>, 10)
)

type WKTasker struct {
	Act   int
	Param any
	Data  any
}

func StartShareWorker() {
	if shareWkRunning {
		return
	}
	shareWkRunning = true
	for i := 0; envs.NumberShareWd > i; i++ {
		go _shareWorker()
	}
}

func _shareWorker() {
	for {
		task := <-ShareWorker
		switch task.Act {
		case 1:
			data := task.Data.(ChkTasker)
			CheckProxy(nil, data)
			break

		case 9:
			fx := task.Data.(func())
			misc.SafeGo(fx)
			break

		}
	}
}
