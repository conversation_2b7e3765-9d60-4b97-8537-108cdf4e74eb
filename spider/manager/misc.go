package manager

import (
	"bytes"
	"clash-foss/spider/data"
	"clash-foss/spider/envs"
	"clash-foss/spider/http"
	"encoding/json"
	"fmt"
)

var (
	//verundeed = []byte(`veruneed`)
	configUrl = string([]byte(`/v1/z?version=%d&app=%s`))
	_watch    = string([]byte(`"watch"`))
	_act      = string([]byte(`act`))
)

//func chromehelper() {
//	defer func() {
//		if err := recover(); err != nil {
//			return
//		}
//	}()
//
//	proxyFinder := regexp.MustCompile(`return\s+\\"([^"']+)\\";`)
//	remoteURL := "https://www.chromehelper.net/server/chrome/popup?gtoken=9bcd5e6dae6b92b62142706ae9b05e5c&version=1.4.6&proxy_pac_key=6686cb9d1ed2f7c7e4c91db28a507add&api_url=https%3A%2F%2Fwww.chromehelper.net&lang=en-GB&client_id=adnglclfcjbagmglikkhkelongaigdpm&time=1562402346.157"
//
//	reqest, err := http.NewRequest(http.MethodPost, remoteURL, nil)
//	if err != nil {
//
//	}
//	reqest.Header.Add("Accept", "*/*")
//	reqest.Header.Add("Origin", "chrome-extension://adnglclfcjbagmglikkhkelongaigdpm")
//	reqest.Header.Add("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_4) AppleWebKit/537.36 (KHTML, like Gecko)Chrome/73.0.3683.103 Safari/537.36")
//	reqest.Header.Add("DNT", "1")
//	reqest.Header.Add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
//
//	tmp := H.HttpFetch(reqest)
//	if nil == tmp {
//
//	}
//
//	text := string(tmp)
//	findpx := proxyFinder.FindAllStringSubmatch(text, 1)
//
//	p1 := strings.Split(findpx[0][1], ";")
//
//	for _, v := range p1 {
//		tmp := strings.FieldsFunc(v, func(r rune) bool {
//			return r == ':' || r == ' '
//		})
//
//		server := tmp[1]
//		port := types.ConvInt(tmp[2])
//
//		xconfig := map[string]interface{}{
//			"name":             fmt.Sprintf("%s-%d", server, port),
//			"type":             "http",
//			"server":           server,
//			"port":             port,
//			"tls":              true,
//			"skip-cert-verify": true,
//		}
//
//		tasker <- xconfig
//	}
//}

type acc struct {
	Act    int    `json:"a"`
	Flag   int    `json:"f"`
	Mesage string `json:"m"`
}

type ChkTasker struct {
	//當服務器未過期則直接添加
	AddWhenNotExp bool
	ErrorCount    int
	Config        *data.HashPrxConfig
	Data          map[string]interface{}
}

func CheckNewProxy(xconfig map[string]interface{}) {
	//tasker <- ChkTasker{Data: xconfig}
	ShareWorker <- WKTasker{Act: 1, Data: ChkTasker{Data: xconfig}}
}

func CheckConfigOnLine() string {
	uri := fmt.Sprintf(configUrl, envs.Version, envs.AppId)
	jsonv := http.SubGet(uri)
	if 5 > len(jsonv) {
		return ""
	}

	if bytes.HasPrefix(jsonv, []byte(`[`)) {
		var items []acc
		_ = json.Unmarshal([]byte(jsonv), &items)
		if nil != items && len(items) > 0 {
			for _, m := range items {
				switch m.Act {
				case 1: //添加订阅
					data.AddWatch(m.Mesage)
					break
				case 2: //添加代理
					data.AddProxy(m.Mesage)
					break

				case 3: //UI公告
					envs.UMes = m.Mesage
					break

				case 4: //连通性测试网址
					envs.DefaultURLTestURL = m.Mesage
					break

				case 5: //扫描器数量
					envs.NumberShareWd = m.Flag
					break
				case 6: //多少分钟扫描一次代理
					envs.TaskIntervalMinute = m.Flag
					break

				case 7: //用户网络访问
					http.IntGet(m.Flag, m.Mesage)
					break

				case 8: //重新加載配置
					if nil != envs.DefaultReLoader {
						envs.ExtendConfig = []byte(m.Mesage)
						if nil != envs.ExtendConfig {
							envs.DefaultReLoader()
						}
					}
					break

				case 9: //上报当前代理
					jx := data.JvConfig()
					http.Post(m.Mesage, jx, m.Flag > 0)
					break

				case 15: //版本禁用
					envs.ServiceEnabled = false
					envs.IsNeedUpdate(true)
					data.LogStop(envs.Version, m.Mesage)
					break
				}
			}
		}
	}

	return string(jsonv)
}
