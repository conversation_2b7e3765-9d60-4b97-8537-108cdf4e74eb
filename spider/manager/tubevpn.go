package manager

import (
	"bytes"
	httpie "clash-foss/spider/http"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"strconv"
	"strings"
)

type Scratch struct {
	dd string
}

func main() {
	scratch := Scratch{}
	scratch.main()
}

func (this *Scratch) decode(str, str2 string) []byte {
	return this.decrypt(this.parseHexStr2Byte(str), str2)
}

func (this *Scratch) parseHexStr2Byte(str string) []byte {
	if len(str) < 1 {
		return []byte{}
	}

	bArr := make([]byte, len(str)/2)
	for i := 0; i < len(str)/2; i++ {
		i2 := i * 2
		i3 := i2 + 1

		// 将字符串转换为整数
		high, _ := strconv.ParseInt(str[i2:i3], 16, 8)
		low, _ := strconv.ParseInt(str[i3:i2+2], 16, 8)

		// 计算字节值
		bArr[i] = byte(high*16 + low)
	}

	return bArr
}

func (this *Scratch) get_key(str string) []byte {
	if str != "" {
		return []byte(str)
	}
	return make([]byte, 24)
}

func (this *Scratch) decrypt(b_arr []byte, str string) []byte {
	secret_key_spec := this.get_key(str)
	block, err := aes.NewCipher(secret_key_spec)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	decrypted := make([]byte, aes.BlockSize)
	mode := cipher.NewCBCDecrypter(block, secret_key_spec)
	mode.CryptBlocks(decrypted, b_arr)
	return decrypted
}

func (this *Scratch) encode(str1, str2 string) string {
	encrypted_bytes, _ := this.encrypt(str1, str2)
	if encrypted_bytes != nil {
		return this.parseByte2HexStr(encrypted_bytes)
	}
	return ""
}

func (this *Scratch) parseByte2HexStr(b_arr []byte) string {
	stringBuffer := make([]string, len(b_arr))
	for i := 0; i < len(b_arr); i++ {
		hex_string := fmt.Sprintf("%02x", b_arr[i])
		stringBuffer[i] = strings.ToUpper(hex_string)
	}
	return strings.Join(stringBuffer, "")
}
func pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	return append(data, padtext...)
}

func (this *Scratch) encrypt(str1, str2 string) ([]byte, error) {
	secretKeySpec := this.get_key(str2)

	block, err := aes.NewCipher(secretKeySpec)
	if err != nil {
		return nil, err
	}

	bytesToEncrypt := pad([]byte(str1), aes.BlockSize)
	encryptedBytes := make([]byte, len(bytesToEncrypt))

	mode := cipher.NewCBCEncrypter(block, make([]byte, aes.BlockSize))
	mode.CryptBlocks(encryptedBytes, bytesToEncrypt)

	return encryptedBytes, nil
}

/*
	func (s *Scratch) encrypt111(str1, str2 string) []byte {
		secret_key_spec := s.get_key(str2)
		block, err := aes.NewCipher(secret_key_spec)
		if err != nil {
			fmt.Println(err)
			return nil
		}
		bytes_to_encrypt := []byte(str1)
		gcm, err := cipher.NewGCM(block)
		if err != nil {
			fmt.Println(err)
			return nil
		}
		//nonce := gcm.Nonce()
		nonce := gcm.Seal()
		ciphertext := gcm.Seal(nonce, nonce, bytes_to_encrypt, nil)
		return ciphertext
	}

func (s *Scratch) generateRandomString11(length int) string {
	characters := "123456789abcdef"
	result := make([]string, length)
	for i := 0; i < length; i++ {
		random_index := int(rand.Float64() * float64(len(characters)))
		result[i] = string(characters[random_index])
	}
	return strings.Join(result, "")
}
*/

func (this *Scratch) generateRandomString(length int) string {
	characters := "123456789abcdef"
	result := strings.Builder{}

	for i := 0; i < length; i++ {
		max1 := big.NewInt(int64(len(characters)))
		randomIndex, _ := rand.Int(rand.Reader, max1)
		result.WriteByte(characters[randomIndex.Int64()])
	}

	return result.String()
}

/////
/////
/////
/////
/////
/////

func (this *Scratch) main_update() map[string]interface{} {
	imei := this.generateRandomString(32)
	url := "https://api.appapistudio.xyz/node/getInformation_ex"
	fmt.Println("==========>imei: ", imei)
	text := fmt.Sprintf(`{"imei":"%s","platform":"android","version_number":35,"models":"Pixel 3","sdk":"29","m":"D2A5BCEE90DEAD36C4C817E97E6D2606","c":3}`, imei)

	value := this.encode(text, this.dd)
	data := map[string]string{"value": value}
	byte1, _ := json.Marshal(data)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(byte1))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Linux; Android 10; Pixel 3 Build/QQ3A.200805.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.86 Mobile Safari/537.36")

	body := httpie.HttpGetDirect(req)

	var responseData map[string]interface{}

	json.Unmarshal(body, &responseData)
	return responseData
}

func (this *Scratch) export(tubev *WResult) {
	//ssrs := tubev["goserverlist"].([]interface{})
	ssrs := tubev.Goserverlist

	if len(ssrs) < 1 {
		fmt.Println("goserverlist empty")
		return
	}

	clash := map[string]interface{}{
		"port":                8080,
		"socks-port":          7070,
		"allow-lan":           true,
		"mode":                "Global",
		"log-level":           "debug",
		"external-controller": "127.0.0.1:9090",
		"watch":               []map[string]interface{}{{"type": "clean"}},
	}

	proxies := []map[string]interface{}{}
	for _, m := range ssrs {
		t := map[string]interface{}{
			"name":           m.Name,
			"type":           "ssr",
			"server":         m.Host,
			"port":           m.RemotePort,
			"password":       m.Password,
			"cipher":         m.Method,
			"obfs":           m.Obfs,
			"obfs-param":     m.ObfsParam,
			"protocol":       m.Protocol,
			"protocol-param": m.ProtocolParam,
			"udp":            m.Udpport > 0,
		}
		proxies = append(proxies, t)
		fmt.Println(m.Name)
	}

	clash["proxies"] = proxies

	fmt.Println(clash)
}

func (this *Scratch) main() {
	t := this.main_update()

	data := this.decode(t["data"].(string), this.dd)
	var item WResult
	err := json.Unmarshal(data, &item)
	if err != nil {
		panic(err)
	}

	this.export(&item)

	// f, err := os.OpenFile("file.txt", os.O_CREATE|os.O_WRONLY, 0644)
	// if err != nil {
	// 	panic(err)
	// }
	// defer f.Close()
	// _, err = f.WriteString(data)
	// if err != nil {
	// 	panic(err)
	// }

	fmt.Println("=====Finish====>")
}

////////////////////
////////////////////
////////////////////
////////////////////

type WResult struct {
	Version       string `json:"version"`
	VersionNumber int    `json:"version_number"`
	Log           string `json:"log"`
	Downurl       string `json:"downurl"`
	Force         int    `json:"force"`
	Remainingtime int    `json:"remainingtime"`
	ShareNumber   int    `json:"shareNumber"`
	//RUser         struct {
	//	Id                   string `json:"id"`
	//	ExpireIn             string `json:"expireIn"`
	//	RPassword            string `json:"rPassword"`
	//	PasswordModifyLength int    `json:"passwordModifyLength"`
	//	AccountModifyLength  int    `json:"accountModifyLength"`
	//	GiveLoginTime        int    `json:"give_login_time"`
	//	NodeTypeId           int    `json:"nodeTypeId"`
	//	NodeTypename         string `json:"nodeTypename"`
	//	Token                string `json:"token"`
	//	UserName             string `json:"userName"`
	//	Email                string `json:"email"`
	//} `json:"rUser"`
	NoticeList []struct {
		Id       int    `json:"id"`
		Title    string `json:"title"`
		Context  string `json:"context"`
		Type     int    `json:"type"`
		Isenable int    `json:"isenable"`
	} `json:"noticeList"`
	Alertnotice []struct {
		Id       int    `json:"id"`
		Title    string `json:"title"`
		Context  string `json:"context"`
		Type     int    `json:"type"`
		Image    string `json:"image"`
		Isenable int    `json:"isenable"`
		Otype    int    `json:"otype"`
	} `json:"alertnotice"`
	Goserverlist []struct {
		Name          string `json:"name"`
		LocalPort     int    `json:"localPort"`
		Host          string `json:"host"`
		RemotePort    int    `json:"remotePort"`
		Udphost       string `json:"udphost"`
		Udpport       int    `json:"udpport"`
		Password      string `json:"password"`
		Protocol      string `json:"protocol"`
		ProtocolParam string `json:"protocol_param"`
		Obfs          string `json:"obfs"`
		ObfsParam     string `json:"obfs_param"`
		Method        string `json:"method"`
		UrlGroup      string `json:"url_group"`
		Dns           string `json:"dns"`
		ChinaDns      string `json:"china_dns"`
		Status        int    `json:"status"`
		Country       string `json:"country"`
		NodeTypeId    int    `json:"node_type_id"`
	} `json:"goserverlist"`
	Homegoserverlist []struct {
		Name          string `json:"name"`
		LocalPort     int    `json:"localPort"`
		Host          string `json:"host"`
		RemotePort    int    `json:"remotePort"`
		Udphost       string `json:"udphost"`
		Udpport       int    `json:"udpport"`
		Password      string `json:"password"`
		Protocol      string `json:"protocol"`
		ProtocolParam string `json:"protocol_param"`
		Obfs          string `json:"obfs"`
		ObfsParam     string `json:"obfs_param"`
		Method        string `json:"method"`
		UrlGroup      string `json:"url_group"`
		Dns           string `json:"dns"`
		ChinaDns      string `json:"china_dns"`
		Status        int    `json:"status"`
		Country       string `json:"country"`
		NodeTypeId    int    `json:"node_type_id"`
	} `json:"homegoserverlist"`
	//PaymentChannelList []struct {
	//	Id   int    `json:"id"`
	//	Name string `json:"name"`
	//	Type string `json:"type"`
	//} `json:"paymentChannelList"`
	//PaymentChannelList2 []struct {
	//	Id     string `json:"id"`
	//	Name   string `json:"name"`
	//	Type   string `json:"type"`
	//	Open   string `json:"open"`
	//	Choose string `json:"choose"`
	//} `json:"paymentChannelList2"`
	//SetMealList []struct {
	//	Id            int     `json:"id"`
	//	OnlyOne       int     `json:"onlyOne"`
	//	Name          string  `json:"name"`
	//	Oneday        string  `json:"oneday"`
	//	Amount        float64 `json:"amount"`
	//	AmountY       float64 `json:"amountY"`
	//	AmountDollar  float64 `json:"amountDollar"`
	//	AmountYDollar float64 `json:"amountYDollar"`
	//	AmountUnit    string  `json:"amountUnit"`
	//	Content       string  `json:"content"`
	//} `json:"setMealList"`
	//SetMealList2 []struct {
	//	Id      string `json:"id"`
	//	Name    string `json:"name"`
	//	Oneday  string `json:"oneday"`
	//	Amount  string `json:"amount"`
	//	Content string `json:"content"`
	//	Off     string `json:"off"`
	//	Choose  string `json:"choose"`
	//} `json:"setMealList2"`
	IsNative  int    `json:"isNative"`
	IsSignIn  int    `json:"isSignIn"`
	NativeKey string `json:"nativeKey"`
	Navurl    string `json:"navurl"`
	Weburl    string `json:"weburl"`
}
