# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/*

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# go mod vendor
vendor

# GoLand
.idea/*

# macOS file
.DS_Store

# test suite
test/config/cache*

# docs site generator
node_modules
package-lock.json
pnpm-lock.yaml

# docs site cache
docs/.vitepress/cache

# docs site build files
docs/.vitepress/dist
/spider/app/androidaar/roxyp-sources.jar
/spider/app/androidaar/roxyp.aar
/spider/ui/a.db
/spider/ui/cache.db
/spider/ui/Country.mmdb
/spider/ui/d.db
