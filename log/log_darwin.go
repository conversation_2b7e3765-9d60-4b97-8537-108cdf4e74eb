//go:build darwin

package log

import (
	log "github.com/sirupsen/logrus"
	"os"
)

/* * /
//log "github.com/sirupsen/logrus"
func Infoln(format string, v ...interface{})  {}
func Warnln(format string, v ...interface{})  {}
func Errorln(format string, v ...interface{}) {}
func Debugln(format string, v ...interface{}) {}
func Fatalln(format string, v ...interface{}) {}
func print(data *Event)                       {}

// */

/* */
func init() {
	log.SetOutput(os.Stdout)
	log.SetLevel(log.DebugLevel)
}
func Infoln(format string, v ...interface{}) {
	event := newLog(INFO, format, v...)
	logCh <- event
	print(event)
}

func Warnln(format string, v ...interface{}) {
	event := newLog(WARNING, format, v...)
	logCh <- event
	print(event)
}

func Errorln(format string, v ...interface{}) {
	event := newLog(ERROR, format, v...)
	logCh <- event
	print(event)
}

func Debugln(format string, v ...interface{}) {
	event := newLog(DEBUG, format, v...)
	logCh <- event
	print(event)
}

func Fatalln(format string, v ...interface{}) {
	log.Fatalf(format, v...)
}

func print(data Event) {
	if data.LogLevel < level {
		return
	}

	switch data.LogLevel {
	case INFO:
		log.Infoln(data.Payload)
	case WARNING:
		log.Warnln(data.Payload)
	case ERROR:
		log.Errorln(data.Payload)
	case DEBUG:
		log.Debugln(data.Payload)
	}
}

// */
