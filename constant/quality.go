package constant

import (
	"go.uber.org/atomic"
	"sync"
)

var (
	qlock            sync.RWMutex
	servers          = make([]*ServerQuality, 0, 100)
	AutoFastReloader func()
	nowSec           int64 = 0
)

type ServerQuality struct {
	Transfer     *atomic.Int64
	Success      *atomic.Int32
	Error        *atomic.Int32
	ErrorCount   int
	OkCount      int
	LastOkcc     int
	LastEcc      int
	MaxSpeed     int64
	LastTransfer int64
	Delay        uint16
	tickSec      int64 //速度统计秒
}

func NewServerQuality() *ServerQuality {
	ss := &ServerQuality{
		Transfer:   atomic.NewInt64(0),
		Success:    atomic.NewInt32(0),
		Error:      atomic.NewInt32(0),
		ErrorCount: 0,
	}
	qlock.Lock()
	servers = append(servers, ss)
	qlock.Unlock()
	return ss
}

func (ss *ServerQuality) Report(err error) {
	ss.LastOkcc = ss.OkCount
	ss.OkCount = 0
	ss.ErrorCount += 1
	ss.Error.Add(1)
	if "EOF" == err.Error() {
		ss.ErrorCount += 2
	}

	//如果连续错误则切换代理
	if ss.ErrorCount > 7 && nil != AutoFastReloader {
		ss.MaxSpeed = ss.MaxSpeed / 2
		AutoFastReloader()
	}
	ss.calcSpeed()
}
func (ss *ServerQuality) OnTransfer(total int64, err error) {
	if total > 0 {
		ss.Transfer.Add(total)
	}
	if nil == err {
		ss.LastEcc = ss.ErrorCount
		ss.ErrorCount = 0
		ss.OkCount += 1
		ss.Success.Add(1)
	} else {
		ss.Report(err)
	}
	ss.calcSpeed()
}

func (this *ServerQuality) calcSpeed() {
	ts := nowSec - this.tickSec
	total := this.Transfer.Load()
	sec := total - this.LastTransfer
	this.LastTransfer = total
	this.tickSec = nowSec

	//相关1表示连续的，上1秒的记录
	//不是连续的不作统计
	if ts > 1 {
		this.MaxSpeed = (this.MaxSpeed + sec) / 3
		return
	}

	if total > this.MaxSpeed {
		this.MaxSpeed = (this.MaxSpeed + sec) / 2
	} else if this.ErrorCount > 3 {
		this.MaxSpeed = (this.MaxSpeed + sec) / 3
	}
}

func QualitySpeed(now int64) {
	nowSec = now
	//qlock.RLock()
	//for _, ss := range servers {
	//	total := ss.Transfer.Load()
	//	sec := total - ss.LastTransfer
	//	ss.LastTransfer = total
	//	if total > ss.MaxSpeed {
	//		ss.MaxSpeed = (ss.MaxSpeed + sec) / 2
	//	} else if ss.ErrorCount > 3 {
	//		ss.MaxSpeed = (ss.MaxSpeed + sec) / 2
	//	}
	//}
	//qlock.RUnlock()
}
