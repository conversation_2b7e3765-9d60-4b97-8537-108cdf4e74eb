package tunnel

import (
	"clash-foss/adapter"
	C "clash-foss/constant"
	"clash-foss/spider/envs"
	"sort"
)

var (
	MaxSize      = 100
	proxiesElems = make([]C.Proxy, 0, MaxSize)
)

type PList []C.Proxy

func (s PList) Len() int      { return len(s) }
func (s PList) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s PList) Less(i, j int) bool {
	aa := s[i].Score()
	bb := s[j].Score()

	//fmt.Println("====SCORE====>", aa, bb)
	if aa == bb {
		a := s[i].LastDelay()
		b := s[j].LastDelay()
		return a < b
	}

	return aa > bb

	//a := s[i].LastDelay()
	//b := s[j].LastDelay()
	//if 1 > a {
	//	a = 65535
	//}
	//if 1 > b {
	//	b = 65535
	//}
	//
	//return a < b
}

func proxyId(proxy C.Proxy) string {
	name := proxy.Addr()
	if 1 > len(name) {
		return name
	}
	//return strings.Split(name, ":")[0]
	return name
}
func AddNewProxy(proxy C.Proxy) {
	configMux.Lock()
	defer configMux.Unlock()
	name := proxyId(proxy)
	if 1 > len(name) {
		//系统名，DIRECT等
		name = proxy.Name()
		if _, ok := proxies[name]; !ok {
			proxies[name] = proxy
		}
		return
	}
	if _, ok := proxies[name]; !ok {
		proxies[name] = proxy
		proxiesElems = append(proxiesElems, proxy)
	}

	envs.Total = LoadProxiesSize()
}

func HasProxy(proxy C.Proxy) bool {
	configMux.RLock()
	hasp := false
	name := proxyId(proxy)
	if _, ok := proxies[name]; ok {
		hasp = true
	}
	configMux.RUnlock()
	return hasp
}
func LoadProxiesSize() int {
	return len(proxiesElems)
}
func LoadOnlyProxies() []C.Proxy {
	return proxiesElems
}

func LoadSortedProxies(total int) []C.Proxy {
	items := LoadOnlyProxies()

	if nil == items || 1 > len(items) {
		return nil
	}

	sort.Sort(PList(items))
	size := len(items)
	if size > total {
		size = total
	}
	return items[0:size]
}

//func collectProviders(providers []provider.ProxyProvider) []C.Proxy {
//	result := make([]C.Proxy, 0, 128)
//
//	for _, p := range providers {
//		for _, px := range p.Proxies() {
//			//name := px.Name()
//			//title := name
//			//subtitle := px.Type().String()
//
//			result = append(result, px)
//		}
//	}
//
//	return result
//}

func IsProxyOnly(proxy C.Proxy) bool {
	_, ok := proxy.(*adapter.Proxy)
	if !ok {
		return false
	}

	t := proxy.Type()

	if t == C.Shadowsocks ||
		t == C.ShadowsocksR ||
		t == C.Snell ||
		t == C.Socks5 ||
		t == C.Http ||
		t == C.Vmess ||
		t == C.Trojan {
		return true
	}

	return false
}

func SortProxies(items []C.Proxy) []C.Proxy {
	if nil == items || 1 > len(items) {
		return nil
	}
	sort.Sort(PList(items))
	return items
}
