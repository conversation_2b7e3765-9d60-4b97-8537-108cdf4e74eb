package statistic

//
//import (
//	"go.uber.org/atomic"
//	"sync"
//)
//
//var (
//	maxDelay       = uint16(65535)
//	Quality        = &QualityManager{}
//	AutoFastLoader func()
//)
//
//type ServerQuality struct {
//	transfer   *atomic.Int64
//	suces      *atomic.Int32
//	error      *atomic.Int32
//	errorCount int
//	okCount    int
//	lastOkcc   int
//	lastEcc    int
//	maxSpeed   int64
//	temptf     int64
//}
//
//type QualityManager struct {
//	servers sync.Map
//}
//
//func (this *QualityManager) get(id string) *ServerQuality {
//	if 1 > len(id) {
//		return nil
//	}
//	var s *ServerQuality
//	if t, ok := this.servers.Load(id); ok {
//		s = t.(*ServerQuality)
//	} else {
//		s = &ServerQuality{
//			transfer:   atomic.NewInt64(0),
//			suces:      atomic.NewInt32(0),
//			error:      atomic.NewInt32(0),
//			errorCount: 0,
//		}
//		this.servers.Store(id, s)
//	}
//	return s
//}
//
//func (this *QualityManager) handelSpeed() {
//	this.servers.Range(func(key, value any) bool {
//		ss := value.(*ServerQuality)
//		total := ss.transfer.Load()
//		sec := total - ss.temptf
//		ss.temptf = total
//		if total > ss.maxSpeed {
//			ss.maxSpeed = (ss.maxSpeed + sec) / 2
//		} else if ss.errorCount > 3 {
//			ss.maxSpeed = (ss.maxSpeed + sec) / 2
//		}
//
//		return true
//	})
//}
