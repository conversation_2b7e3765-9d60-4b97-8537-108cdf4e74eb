package tunnel

import (
	C "clash-foss/constant"
	"clash-foss/log"
	"clash-foss/tunnel/statistic"
	"context"
)

func findAliveProxy(connCtx C.ConnContext, metadata *C.Metadata) (<PERSON><PERSON>, <PERSON><PERSON>, C<PERSON>Rule, error) {
	var proxy C.Proxy
	var rule C.Rule
	var err error
	var ctx context.Context
	var cancel context.CancelFunc
	var remoteConn C.Conn

	for i := 0; i < 3; i++ {
		proxy, rule, err = resolveMetadata(connCtx, metadata)
		if err != nil {
			log.Warnln("[Metadata] parse failed: %s", err.Error())
			continue
		}

		ctx, cancel = context.WithTimeout(context.Background(), C.DefaultTCPTimeout)
		remoteConn, err = proxy.DialContext(ctx, metadata.Pure())

		if err != nil || nil == remoteConn {
			proxy.URLTest(nil, "c")

			if rule == nil {
				log.Errorln("[TCP] dial %s to %s error: %s", proxy.Name(), metadata.RemoteAddress(), err.Error())
			} else {
				log.Errorln("[TCP] dial %s (match %s/%s) to %s error: %s", proxy.Name(), rule.RuleType().String(), rule.Payload(), metadata.RemoteAddress(), err.Error())
			}
			cancel()
			continue
		}
		remoteConn = statistic.NewTCPTracker(remoteConn, statistic.DefaultManager, metadata, rule, proxy)

		cancel()
		return proxy, remoteConn, rule, err
	}
	return nil, nil, nil, err
}

//func handleSocketEx(ctx C.ConnContext, outbound net.Conn) {
//	left := unwrap(ctx.Conn())
//	right := unwrap(outbound)
//
//	if relayHijack(left, right) {
//		return
//	}
//
//	rec := N.RelayEx(left, right)
//
//	if rec >= 7 && nil != AutoFastLoader {
//		AutoFastLoader()
//	}
//}
