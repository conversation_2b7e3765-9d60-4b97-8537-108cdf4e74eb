package hooker

import (
	C "clash-foss/constant"
	"net/http"
)

var (
	<PERSON> IHooker = nil
)

type IHooker interface {
	NewRequest(url string) (*http.Request, error)
	IsMobile() bool
	GetAliveProxy() interface{}
	GetFastProxy() interface{}
	HealthCheckAll(proxies []C.Proxy, urls ...string)
	RegisterRawConfig(cfg []byte)
	ParseProxies(groups *[]map[string]interface{}, proxies *[]map[string]interface{})
	FastChoose(proxies *map[string]C.Proxy) C.Proxy
	LoadConfigedProxy(proxies *[]C.Proxy) []C.Proxy
}

func AddHooker(hook IHooker) {
	Hooker = hook
}
