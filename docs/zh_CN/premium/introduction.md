---
sidebarTitle: 简介
sidebarOrder: 1
---

# 简介

在过去, 只有一个开源版本的 Clash, 直到一些 [不当使用和再分发](https://github.com/Dreamacro/clash/issues/541#issuecomment-672029110) 的 Clash 出现. 从那时起, 我们决定分叉 Clash 并在私有 GitHub 存储库中开发更高级的功能.

不要担心 - Premium 内核将保持免费, 并且其源代码的安全性通过多个可信的开发人员相互审查以保证.

## 有什么区别？

Premium 内核是开源 Clash 内核的 Fork 分支, 增加了以下功能:

- [TUN 设备](/zh_CN/premium/tun-device) 支持 `auto-redir` 和 `auto-route`
- [eBPF 重定向到 TUN](/zh_CN/premium/ebpf)
- [Rule Providers 规则集](/zh_CN/premium/rule-providers)
- [Script 脚本](/zh_CN/premium/script)
- [Script Shotcuts 脚本捷径](/zh_CN/premium/script-shortcuts)
- [用户空间 Wireguard](/zh_CN/premium/userspace-wireguard)
- [性能分析引擎](/zh_CN/premium/the-profiling-engine)

## 获取副本

您可以从 [GitHub Releases](https://github.com/Dreamacro/clash/releases/tag/premium) 下载最新的 Clash Premium 二进制文件.
