---
sidebarTitle: "Feature: Userspace Wireguard"
sidebarOrder: 7
---

# Userspace Wireguard

Due to the dependency on gvisor TCP/IP stack, Wireguard outbound is currently only available in the Premium core.

```yaml
proxies:
  - name: "wg"
    type: wireguard
    server: 127.0.0.1
    port: 443
    ip: **********
    # ipv6: your_ipv6
    private-key: eCtXsJZ27+4PbhDkHnB923tkUn2Gj59wZw5wFA75MnU=
    public-key: Cr8hWlKvtDt7nrvf+f0brNQQzabAqrjfBvas9pmowjo=
    # preshared-key: base64
    # remote-dns-resolve: true # remote resolve DNS with `dns` field, default is true
    # dns: [*******, *******]
    # mtu: 1420
    udp: true
```
