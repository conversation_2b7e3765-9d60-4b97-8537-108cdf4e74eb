---
sidebarTitle: Introduction
sidebarOrder: 1
---

# Introduction

In the past, there was only one open-source version of Clash, until some [improper uses and redistributions](https://github.com/Dreamacro/clash/issues/541#issuecomment-672029110) of Clash arose. From that, we decided to fork Clash and develop the more advanced features in a private GitHub repository.

Don't worry just yet - the Premium core will stay free of charge, and the security is enforced with peer reviews from multiple credible developers.

## What's the difference?

The Premium core is a fork of the open-source Clash core with the addition of the following features:

- [TUN Device](/premium/tun-device) with the support of `auto-redir` and `auto-route`
- [eBPF Redirect to TUN](/premium/ebpf)
- [Rule Providers](/premium/rule-providers)
- [Script](/premium/script)
- [Script Shortcuts](/premium/script-shortcuts)
- [Userspace Wireguard](/premium/userspace-wireguard)
- [The Profiling Engine](/premium/the-profiling-engine)

## Obtaining a Copy

You can download the latest Clash Premium binaries from [GitHub Releases](https://github.com/Dreamacro/clash/releases/tag/premium).
