package net

import (
	"io"
	"net"
	"time"
)

var (
	ec1 = 0
	ec2 = 0
)

func RelayEx(leftConn, rightConn net.Conn) int {
	ch := make(chan error)

	go func() {
		// Wrapping to avoid using *net.TCPConn.(ReadFrom)
		// See also https://clash-foss/pull/1209
		_, err := io.Copy(WriteOnlyWriter{Writer: leftConn}, ReadOnlyReader{Reader: rightConn})
		if err != nil {
			ec1 += 1
		} else {
			ec1 = 0
		}

		leftConn.SetReadDeadline(time.Now())
		ch <- err
	}()

	_, err := io.Copy(WriteOnlyWriter{Writer: rightConn}, ReadOnlyReader{Reader: leftConn})

	if err != nil {
		ec2 += 1
	} else {
		ec2 = 0
	}

	rightConn.SetReadDeadline(time.Now())
	<-ch

	return ec1 + ec2
}

//func RelayEx2(leftConn, rightConn net.Conn) int {
//	ch := make(chan error)
//
//	go func() {
//		buf := pool.Get(pool.RelayBufferSize)
//		// Wrapping to avoid using *net.TCPConn.(ReadFrom)
//		// See also https://clash-foss/pull/1209
//		_, err := io.CopyBuffer(WriteOnlyWriter{Writer: leftConn}, ReadOnlyReader{Reader: rightConn}, buf)
//		if err != nil {
//			ec1 += 1
//		} else {
//			ec1 = 0
//		}
//		pool.Put(buf)
//		leftConn.SetReadDeadline(time.Now())
//		ch <- err
//	}()
//
//	buf := pool.Get(pool.RelayBufferSize)
//	_, err := io.CopyBuffer(WriteOnlyWriter{Writer: rightConn}, ReadOnlyReader{Reader: leftConn}, buf)
//
//	if err != nil {
//		ec2 += 1
//	} else {
//		ec2 = 0
//	}
//
//	pool.Put(buf)
//	rightConn.SetReadDeadline(time.Now())
//
//	<-ch
//
//	return ec1 + ec2
//}
