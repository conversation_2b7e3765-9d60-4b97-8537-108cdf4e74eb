参考 autofastdt.go 开发代理选举算法，目标是使用多个代理实现稳定性和速度最大化：
- 需要实现 `ProxyAdapter` 接口；
- 通过配置支持2种模式：选举模式（使用上次选举出来的代理直到连续错误），混合模式（使用topN个代理，但速度越快质量越高的代理使用频率越高）；
- 集中管理代码中用到的配置常量；
- 并发连接测试: 无可用代理时（首次连接、所有代理标记为离线、未初始化前等等），对当前请求同时尝试多个代理，选出速度最快的代理，并保存排序结果；
- 权重分配: 根据代理速度自动分配权重，优先使用速度快、稳定性高的代理；
- 实时监控：通过包装tcp协议的Read、Write方法收集代理使用状态数据（错误率，速度等），进一步评估代理的质量；
- 质量评估：根据代理的使用状态数据，评估代理的质量，比如速度、稳定性、可用性等排序；
- 重试机制: 当tcp Read、Write、连接时发生错误（连续错误N次），自动切换到其它代理重试当前请求；
- 故障转移: 当前代理连续发生M次错误，则自动切换到其它代理重试，并把当前代理标记为离线状态；
- 错误反馈：当使用中的代理连续出现错误（错误可能是暂时的，需要有一定的容忍度），降低此代理的权重；
- 离线恢复：当代理离线时，自动切换到其它代理重试，并由统一线程定期测试离线代理，恢复后重新标记成正常状态；
- 防止死锁：减少锁的使用，尽量使用其它方案而不是锁来实现线程安全（有时甚至有些不需保证线程安全）；
- 目标：我们的目标是兼顾稳定性的同时速度最大化，你需要按这个目标去实现深度思考和实现相关代码；


调整代理运行时的状态数据收集：
- 在 `tracker.go` 的 Read、Write方法中收集代理运行状态的数据（错误，速度等），可以参考 `ServerQuality.OnTransfer`方法；
- 目标：我们的目标是兼顾稳定性的同时速度最大化，你需要按这个目标去实现深度思考和实现相关代码；
